import { test, expect, Page } from '@playwright/test';

// Configurações de teste
const DASHBOARD_URL = process.env.DASHBOARD_URL || 'http://localhost:3000/dashboard';
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000';

// Helper functions
async function waitForKpisToLoad(page: Page) {
  // Aguarda os KPIs carregarem
  await page.waitForSelector('[data-testid^="kpi-card-"]', { timeout: 10000 });
  
  // Aguarda os loading states desaparecerem
  await page.waitForFunction(() => {
    const loadingElements = document.querySelectorAll('[data-testid*="loading"]');
    return loadingElements.length === 0;
  }, { timeout: 15000 });
}

async function mockApiResponses(page: Page) {
  // Mock das respostas da API para testes consistentes
  await page.route(`${API_BASE_URL}/api/dashboard/kpis*`, async route => {
    const mockResponse = {
      kpis: [
        {
          id: 'total_volume',
          title: 'Volume Total',
          description: 'Volume total negociado',
          currentValue: 26800000000,
          format: 'currency',
          changePercent: 15.2,
          trend: 'up',
          chartType: 'area',
          chartData: [
            { name: 'Jan', value: 25000000000 },
            { name: 'Feb', value: 26800000000 },
          ],
          isPriority: true,
          order: 1,
          category: 'volume',
        },
        {
          id: 'average_ticket',
          title: 'Ticket Médio',
          description: 'Valor médio por operação',
          currentValue: 1100000,
          format: 'currency',
          changePercent: -2.1,
          trend: 'down',
          chartType: 'line',
          chartData: [
            { name: 'Jan', value: 1120000 },
            { name: 'Feb', value: 1100000 },
          ],
          isPriority: true,
          order: 2,
          category: 'performance',
        },
      ],
      total_count: 2,
      sector: 'cambio',
      client_id: 'L2M',
      timeframe: 'week',
      generated_at: new Date().toISOString(),
    };

    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockResponse),
    });
  });
}

test.describe('Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Setup mocks antes de cada teste
    await mockApiResponses(page);
  });

  test('should load dashboard with KPIs successfully', async ({ page }) => {
    await page.goto(DASHBOARD_URL);

    // Verifica se o título da página está correto
    await expect(page).toHaveTitle(/Dashboard/);

    // Aguarda os KPIs carregarem
    await waitForKpisToLoad(page);

    // Verifica se os KPIs principais estão visíveis
    await expect(page.locator('[data-testid="kpi-card-total_volume"]')).toBeVisible();
    await expect(page.locator('[data-testid="kpi-card-average_ticket"]')).toBeVisible();

    // Verifica se os valores estão sendo exibidos
    await expect(page.locator('text=Volume Total')).toBeVisible();
    await expect(page.locator('text=Ticket Médio')).toBeVisible();
  });

  test('should handle filter changes correctly', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await waitForKpisToLoad(page);

    // Localiza e clica no filtro de timeframe
    const timeframeSelect = page.locator('[data-testid="timeframe-select"]');
    await timeframeSelect.click();

    // Seleciona "month"
    await page.locator('text=Mês').click();

    // Verifica se o loading state aparece
    await expect(page.locator('[data-testid="filter-loading"]')).toBeVisible();

    // Aguarda o loading desaparecer
    await page.waitForSelector('[data-testid="filter-loading"]', { state: 'hidden' });

    // Verifica se os KPIs foram atualizados
    await expect(page.locator('[data-testid="kpi-card-total_volume"]')).toBeVisible();
  });

  test('should handle currency filter changes', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await waitForKpisToLoad(page);

    // Localiza e clica no filtro de moeda
    const currencySelect = page.locator('[data-testid="currency-select"]');
    await currencySelect.click();

    // Seleciona "USD"
    await page.locator('text=USD').click();

    // Aguarda a atualização
    await page.waitForTimeout(1000);

    // Verifica se os KPIs ainda estão visíveis
    await expect(page.locator('[data-testid="kpi-card-total_volume"]')).toBeVisible();
  });

  test('should refresh dashboard data', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await waitForKpisToLoad(page);

    // Clica no botão de refresh
    const refreshButton = page.locator('[data-testid="refresh-button"]');
    await refreshButton.click();

    // Verifica se o ícone de loading aparece
    await expect(page.locator('[data-testid="refresh-button"] .animate-spin')).toBeVisible();

    // Aguarda o refresh completar
    await page.waitForSelector('[data-testid="refresh-button"] .animate-spin', { state: 'hidden' });

    // Verifica se os KPIs ainda estão visíveis
    await expect(page.locator('[data-testid="kpi-card-total_volume"]')).toBeVisible();
  });

  test('should open and close add KPI modal', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await waitForKpisToLoad(page);

    // Clica no botão "Adicionar KPI"
    const addKpiButton = page.locator('[data-testid="add-kpi-button"]');
    await addKpiButton.click();

    // Verifica se o modal abriu
    await expect(page.locator('[data-testid="add-kpi-modal"]')).toBeVisible();

    // Fecha o modal clicando no X
    const closeButton = page.locator('[data-testid="close-modal-button"]');
    await closeButton.click();

    // Verifica se o modal fechou
    await expect(page.locator('[data-testid="add-kpi-modal"]')).not.toBeVisible();
  });

  test('should handle KPI card interactions', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await waitForKpisToLoad(page);

    const kpiCard = page.locator('[data-testid="kpi-card-total_volume"]');

    // Verifica se o card está visível
    await expect(kpiCard).toBeVisible();

    // Hover sobre o card para mostrar controles
    await kpiCard.hover();

    // Verifica se os controles aparecem
    await expect(page.locator('[data-testid="kpi-controls-total_volume"]')).toBeVisible();

    // Clica no botão de editar
    const editButton = page.locator('[data-testid="edit-kpi-total_volume"]');
    await editButton.click();

    // Verifica se o modal de edição abriu
    await expect(page.locator('[data-testid="edit-kpi-modal"]')).toBeVisible();
  });

  test('should handle responsive layout', async ({ page }) => {
    // Testa layout desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto(DASHBOARD_URL);
    await waitForKpisToLoad(page);

    // Verifica se o grid está usando layout desktop
    const gridContainer = page.locator('[data-testid="kpi-bento-grid"]');
    await expect(gridContainer).toHaveClass(/grid-cols-12/);

    // Testa layout mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);

    // Verifica se o layout se adapta
    await expect(gridContainer).toBeVisible();
    
    // Verifica se os KPIs ainda estão visíveis em mobile
    await expect(page.locator('[data-testid="kpi-card-total_volume"]')).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Mock de erro na API
    await page.route(`${API_BASE_URL}/api/dashboard/kpis*`, async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Internal server error' }),
      });
    });

    await page.goto(DASHBOARD_URL);

    // Verifica se a mensagem de erro aparece
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('text=Erro ao carregar KPIs')).toBeVisible();

    // Verifica se o botão de retry está presente
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
  });

  test('should handle loading states correctly', async ({ page }) => {
    // Mock de resposta lenta
    await page.route(`${API_BASE_URL}/api/dashboard/kpis*`, async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          kpis: [],
          total_count: 0,
          sector: 'cambio',
          client_id: 'L2M',
          timeframe: 'week',
          generated_at: new Date().toISOString(),
        }),
      });
    });

    await page.goto(DASHBOARD_URL);

    // Verifica se o loading state inicial aparece
    await expect(page.locator('[data-testid="dashboard-loading"]')).toBeVisible();

    // Aguarda o loading desaparecer
    await page.waitForSelector('[data-testid="dashboard-loading"]', { state: 'hidden', timeout: 5000 });
  });

  test('should maintain state during navigation', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await waitForKpisToLoad(page);

    // Muda filtros
    const timeframeSelect = page.locator('[data-testid="timeframe-select"]');
    await timeframeSelect.click();
    await page.locator('text=Mês').click();

    // Navega para outra página e volta
    await page.goto('/');
    await page.goBack();

    // Verifica se os filtros foram mantidos
    await expect(page.locator('[data-testid="timeframe-select"]')).toContainText('Mês');
  });

  test('should handle keyboard navigation', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await waitForKpisToLoad(page);

    // Testa navegação por tab
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');

    // Verifica se o foco está em um elemento interativo
    const focusedElement = page.locator(':focus');
    await expect(focusedElement).toBeVisible();

    // Testa ativação por Enter
    await page.keyboard.press('Enter');
  });
});

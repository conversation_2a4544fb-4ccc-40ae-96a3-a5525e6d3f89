import { test, expect, Page } from '@playwright/test';
import { performance } from 'perf_hooks';

// Configurações de performance
const PERFORMANCE_THRESHOLDS = {
  INITIAL_LOAD: 3000, // 3 segundos
  FILTER_CHANGE: 1000, // 1 segundo
  REFRESH: 2000, // 2 segundos
  CHART_RENDER: 500, // 500ms
  MEMORY_LEAK_THRESHOLD: 50 * 1024 * 1024, // 50MB
};

const DASHBOARD_URL = process.env.DASHBOARD_URL || 'http://localhost:3000/dashboard';

// Helper functions
async function measurePageLoad(page: Page): Promise<number> {
  const startTime = performance.now();
  await page.goto(DASHBOARD_URL);
  await page.waitForSelector('[data-testid^="kpi-card-"]', { timeout: 10000 });
  const endTime = performance.now();
  return endTime - startTime;
}

async function measureFilterChange(page: Page): Promise<number> {
  const startTime = performance.now();
  
  // Muda o filtro
  const timeframeSelect = page.locator('[data-testid="timeframe-select"]');
  await timeframeSelect.click();
  await page.locator('text=Mês').click();
  
  // Aguarda a atualização
  await page.waitForSelector('[data-testid="filter-loading"]', { state: 'hidden' });
  
  const endTime = performance.now();
  return endTime - startTime;
}

async function measureRefresh(page: Page): Promise<number> {
  const startTime = performance.now();
  
  // Clica no refresh
  const refreshButton = page.locator('[data-testid="refresh-button"]');
  await refreshButton.click();
  
  // Aguarda o refresh completar
  await page.waitForSelector('[data-testid="refresh-button"] .animate-spin', { state: 'hidden' });
  
  const endTime = performance.now();
  return endTime - startTime;
}

async function getMemoryUsage(page: Page): Promise<number> {
  const metrics = await page.evaluate(() => {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  });
  return metrics;
}

async function mockFastApiResponses(page: Page) {
  const mockKpis = Array.from({ length: 20 }, (_, i) => ({
    id: `kpi_${i}`,
    title: `KPI ${i}`,
    description: `Description for KPI ${i}`,
    currentValue: Math.random() * 1000000,
    format: 'currency',
    changePercent: (Math.random() - 0.5) * 20,
    trend: Math.random() > 0.5 ? 'up' : 'down',
    chartType: 'area',
    chartData: Array.from({ length: 30 }, (_, j) => ({
      name: `Day ${j}`,
      value: Math.random() * 1000000,
    })),
    isPriority: i < 6,
    order: i,
    category: 'performance',
  }));

  await page.route('**/api/dashboard/kpis*', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        kpis: mockKpis,
        total_count: mockKpis.length,
        sector: 'cambio',
        client_id: 'L2M',
        timeframe: 'week',
        generated_at: new Date().toISOString(),
      }),
    });
  });
}

test.describe('Dashboard Performance Tests', () => {
  test.beforeEach(async ({ page }) => {
    await mockFastApiResponses(page);
  });

  test('should load dashboard within performance threshold', async ({ page }) => {
    const loadTime = await measurePageLoad(page);
    
    console.log(`Dashboard load time: ${loadTime.toFixed(2)}ms`);
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.INITIAL_LOAD);
    
    // Verifica se todos os KPIs carregaram
    const kpiCards = page.locator('[data-testid^="kpi-card-"]');
    const count = await kpiCards.count();
    expect(count).toBeGreaterThan(0);
  });

  test('should handle filter changes efficiently', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await page.waitForSelector('[data-testid^="kpi-card-"]');
    
    const filterTime = await measureFilterChange(page);
    
    console.log(`Filter change time: ${filterTime.toFixed(2)}ms`);
    expect(filterTime).toBeLessThan(PERFORMANCE_THRESHOLDS.FILTER_CHANGE);
  });

  test('should refresh data efficiently', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await page.waitForSelector('[data-testid^="kpi-card-"]');
    
    const refreshTime = await measureRefresh(page);
    
    console.log(`Refresh time: ${refreshTime.toFixed(2)}ms`);
    expect(refreshTime).toBeLessThan(PERFORMANCE_THRESHOLDS.REFRESH);
  });

  test('should render charts efficiently', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await page.waitForSelector('[data-testid^="kpi-card-"]');
    
    const startTime = performance.now();
    
    // Aguarda todos os gráficos renderizarem
    await page.waitForSelector('svg', { timeout: 5000 });
    
    const endTime = performance.now();
    const chartRenderTime = endTime - startTime;
    
    console.log(`Chart render time: ${chartRenderTime.toFixed(2)}ms`);
    expect(chartRenderTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CHART_RENDER);
  });

  test('should not have memory leaks during navigation', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await page.waitForSelector('[data-testid^="kpi-card-"]');
    
    const initialMemory = await getMemoryUsage(page);
    
    // Simula navegação e mudanças de filtro múltiplas vezes
    for (let i = 0; i < 10; i++) {
      // Muda filtros
      const timeframeSelect = page.locator('[data-testid="timeframe-select"]');
      await timeframeSelect.click();
      await page.locator('text=Semana').click();
      await page.waitForTimeout(100);
      
      await timeframeSelect.click();
      await page.locator('text=Mês').click();
      await page.waitForTimeout(100);
      
      // Força garbage collection se disponível
      await page.evaluate(() => {
        if ('gc' in window) {
          (window as any).gc();
        }
      });
    }
    
    const finalMemory = await getMemoryUsage(page);
    const memoryIncrease = finalMemory - initialMemory;
    
    console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    expect(memoryIncrease).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_LEAK_THRESHOLD);
  });

  test('should handle large datasets efficiently', async ({ page }) => {
    // Mock com dataset grande
    const largeKpis = Array.from({ length: 100 }, (_, i) => ({
      id: `large_kpi_${i}`,
      title: `Large KPI ${i}`,
      description: `Description for large KPI ${i}`,
      currentValue: Math.random() * 1000000,
      format: 'currency',
      changePercent: (Math.random() - 0.5) * 20,
      trend: Math.random() > 0.5 ? 'up' : 'down',
      chartType: 'area',
      chartData: Array.from({ length: 100 }, (_, j) => ({
        name: `Point ${j}`,
        value: Math.random() * 1000000,
      })),
      isPriority: i < 6,
      order: i,
      category: 'performance',
    }));

    await page.route('**/api/dashboard/kpis*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          kpis: largeKpis,
          total_count: largeKpis.length,
          sector: 'cambio',
          client_id: 'L2M',
          timeframe: 'week',
          generated_at: new Date().toISOString(),
        }),
      });
    });

    const startTime = performance.now();
    await page.goto(DASHBOARD_URL);
    await page.waitForSelector('[data-testid^="kpi-card-"]');
    const endTime = performance.now();
    
    const loadTime = endTime - startTime;
    console.log(`Large dataset load time: ${loadTime.toFixed(2)}ms`);
    
    // Deve carregar mesmo com dataset grande em tempo razoável
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.INITIAL_LOAD * 2);
  });

  test('should handle concurrent operations efficiently', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await page.waitForSelector('[data-testid^="kpi-card-"]');
    
    const startTime = performance.now();
    
    // Executa múltiplas operações simultaneamente
    const operations = [
      // Mudança de filtro
      (async () => {
        const timeframeSelect = page.locator('[data-testid="timeframe-select"]');
        await timeframeSelect.click();
        await page.locator('text=Mês').click();
      })(),
      
      // Refresh
      (async () => {
        await page.waitForTimeout(100);
        const refreshButton = page.locator('[data-testid="refresh-button"]');
        await refreshButton.click();
      })(),
      
      // Hover em cards
      (async () => {
        const kpiCards = page.locator('[data-testid^="kpi-card-"]');
        const count = await kpiCards.count();
        for (let i = 0; i < Math.min(count, 5); i++) {
          await kpiCards.nth(i).hover();
          await page.waitForTimeout(50);
        }
      })(),
    ];
    
    await Promise.all(operations);
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    console.log(`Concurrent operations time: ${totalTime.toFixed(2)}ms`);
    expect(totalTime).toBeLessThan(PERFORMANCE_THRESHOLDS.REFRESH * 2);
  });

  test('should maintain performance during extended usage', async ({ page }) => {
    await page.goto(DASHBOARD_URL);
    await page.waitForSelector('[data-testid^="kpi-card-"]');
    
    const performanceMetrics: number[] = [];
    
    // Simula uso prolongado
    for (let i = 0; i < 20; i++) {
      const startTime = performance.now();
      
      // Operações típicas do usuário
      const timeframeSelect = page.locator('[data-testid="timeframe-select"]');
      await timeframeSelect.click();
      await page.locator('text=Semana').click();
      await page.waitForTimeout(100);
      
      const refreshButton = page.locator('[data-testid="refresh-button"]');
      await refreshButton.click();
      await page.waitForSelector('[data-testid="refresh-button"] .animate-spin', { state: 'hidden' });
      
      const endTime = performance.now();
      performanceMetrics.push(endTime - startTime);
    }
    
    // Verifica se a performance não degrada significativamente
    const firstHalf = performanceMetrics.slice(0, 10);
    const secondHalf = performanceMetrics.slice(10);
    
    const firstHalfAvg = firstHalf.reduce((a, b) => a + b) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((a, b) => a + b) / secondHalf.length;
    
    const degradation = (secondHalfAvg - firstHalfAvg) / firstHalfAvg;
    
    console.log(`Performance degradation: ${(degradation * 100).toFixed(2)}%`);
    expect(degradation).toBeLessThan(0.5); // Menos de 50% de degradação
  });

  test('should handle network latency gracefully', async ({ page }) => {
    // Simula latência de rede
    await page.route('**/api/dashboard/kpis*', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000)); // 1s de latência
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          kpis: [],
          total_count: 0,
          sector: 'cambio',
          client_id: 'L2M',
          timeframe: 'week',
          generated_at: new Date().toISOString(),
        }),
      });
    });

    const startTime = performance.now();
    await page.goto(DASHBOARD_URL);
    
    // Verifica se o loading state aparece
    await expect(page.locator('[data-testid="dashboard-loading"]')).toBeVisible();
    
    // Aguarda o carregamento completar
    await page.waitForSelector('[data-testid="dashboard-loading"]', { state: 'hidden' });
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    console.log(`Load time with network latency: ${totalTime.toFixed(2)}ms`);
    
    // Deve ser próximo ao tempo de latência + overhead mínimo
    expect(totalTime).toBeGreaterThan(1000); // Pelo menos a latência
    expect(totalTime).toBeLessThan(2000); // Não muito mais que a latência
  });
});

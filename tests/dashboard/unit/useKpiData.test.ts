import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useKpiData } from '@/hooks/useKpiData';
import { getDashboardKpis } from '@/lib/api';
import { type DashboardFilters } from '@/hooks/useDashboardFilters';

// Mock da API
jest.mock('@/lib/api', () => ({
  getDashboardKpis: jest.fn(),
}));

const mockGetDashboardKpis = getDashboardKpis as jest.MockedFunction<typeof getDashboardKpis>;

// Mock data
const mockKpiData = {
  kpis: [
    {
      id: 'total_volume',
      title: 'Volume Total',
      description: 'Volume total negociado',
      currentValue: 26800000000,
      format: 'currency' as const,
      changePercent: 15.2,
      trend: 'up' as const,
      chartType: 'area' as const,
      chartData: [
        { name: 'Jan', value: 25000000000 },
        { name: 'Feb', value: 26800000000 },
      ],
      isPriority: true,
      order: 1,
      category: 'volume',
    },
    {
      id: 'average_ticket',
      title: 'Ticket Médio',
      description: 'Valor médio por operação',
      currentValue: 1100000,
      format: 'currency' as const,
      changePercent: -2.1,
      trend: 'down' as const,
      chartType: 'line' as const,
      chartData: [
        { name: 'Jan', value: 1120000 },
        { name: 'Feb', value: 1100000 },
      ],
      isPriority: true,
      order: 2,
      category: 'performance',
    },
  ],
  total_count: 2,
  sector: 'cambio',
  client_id: 'L2M',
  timeframe: 'week',
  generated_at: new Date().toISOString(),
};

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useKpiData Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultFilters: DashboardFilters = {
    timeframe: 'week',
    currency: 'all',
  };

  it('should fetch KPI data successfully', async () => {
    mockGetDashboardKpis.mockResolvedValueOnce(mockKpiData);

    const { result } = renderHook(() => useKpiData(defaultFilters), {
      wrapper: createWrapper(),
    });

    expect(result.current.isInitialLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isInitialLoading).toBe(false);
    });

    expect(result.current.kpis).toHaveLength(2);
    expect(result.current.kpis[0].id).toBe('total_volume');
    expect(result.current.error).toBeNull();
  });

  it('should handle API errors gracefully', async () => {
    const errorMessage = 'Failed to fetch KPIs';
    mockGetDashboardKpis.mockRejectedValueOnce(new Error(errorMessage));

    const { result } = renderHook(() => useKpiData(defaultFilters), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isInitialLoading).toBe(false);
    });

    expect(result.current.error).toContain(errorMessage);
    expect(result.current.kpis).toHaveLength(0);
  });

  it('should filter critical KPIs correctly', async () => {
    mockGetDashboardKpis.mockResolvedValueOnce(mockKpiData);

    const { result } = renderHook(() => useKpiData(defaultFilters), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isInitialLoading).toBe(false);
    });

    // Verifica se apenas KPIs críticos são retornados
    const criticalKpiIds = ['total_volume', 'average_ticket', 'average_spread', 'conversion_rate', 'retention_rate', 'gross_margin'];
    result.current.kpis.forEach(kpi => {
      expect(criticalKpiIds).toContain(kpi.id);
    });
  });

  it('should handle filter changes correctly', async () => {
    mockGetDashboardKpis.mockResolvedValueOnce(mockKpiData);

    const { result, rerender } = renderHook(
      ({ filters }) => useKpiData(filters),
      {
        wrapper: createWrapper(),
        initialProps: { filters: defaultFilters },
      }
    );

    await waitFor(() => {
      expect(result.current.isInitialLoading).toBe(false);
    });

    // Muda os filtros
    const newFilters: DashboardFilters = {
      timeframe: 'month',
      currency: 'usd',
    };

    mockGetDashboardKpis.mockResolvedValueOnce(mockKpiData);
    rerender({ filters: newFilters });

    expect(result.current.isFilterChanging).toBe(true);

    await waitFor(() => {
      expect(result.current.isFilterChanging).toBe(false);
    });

    expect(mockGetDashboardKpis).toHaveBeenCalledWith(
      'cambio',
      'L2M',
      'month',
      undefined,
      true,
      'usd'
    );
  });

  it('should provide refresh functionality', async () => {
    mockGetDashboardKpis.mockResolvedValueOnce(mockKpiData);

    const { result } = renderHook(() => useKpiData(defaultFilters), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isInitialLoading).toBe(false);
    });

    // Simula refresh
    mockGetDashboardKpis.mockResolvedValueOnce({
      ...mockKpiData,
      kpis: [
        ...mockKpiData.kpis,
        {
          id: 'new_kpi',
          title: 'Novo KPI',
          description: 'KPI adicionado após refresh',
          currentValue: 100,
          format: 'number' as const,
          trend: 'stable' as const,
          chartType: 'bar' as const,
          chartData: [],
          isPriority: false,
          order: 3,
          category: 'other',
        },
      ],
    });

    await result.current.refreshKpis();

    expect(mockGetDashboardKpis).toHaveBeenCalledTimes(2);
  });

  it('should toggle priority correctly', async () => {
    mockGetDashboardKpis.mockResolvedValueOnce(mockKpiData);

    const { result } = renderHook(() => useKpiData(defaultFilters), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isInitialLoading).toBe(false);
    });

    const initialKpi = result.current.kpis.find(kpi => kpi.id === 'total_volume');
    expect(initialKpi?.isPriority).toBe(true);

    // Toggle priority
    result.current.togglePriority('total_volume');

    const updatedKpi = result.current.kpis.find(kpi => kpi.id === 'total_volume');
    expect(updatedKpi?.isPriority).toBe(false);
  });

  it('should handle loading states correctly', async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise(resolve => {
      resolvePromise = resolve;
    });
    
    mockGetDashboardKpis.mockReturnValueOnce(promise);

    const { result } = renderHook(() => useKpiData(defaultFilters), {
      wrapper: createWrapper(),
    });

    expect(result.current.isInitialLoading).toBe(true);
    expect(result.current.isLoading).toBe(true);

    resolvePromise!(mockKpiData);

    await waitFor(() => {
      expect(result.current.isInitialLoading).toBe(false);
      expect(result.current.isLoading).toBe(false);
    });
  });
});

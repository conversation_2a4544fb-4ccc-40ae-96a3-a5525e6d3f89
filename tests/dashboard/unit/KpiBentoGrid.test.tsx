import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import KpiBentoGrid from '@/components/dashboard/KpiBentoGrid';
import { type KpiData } from '@/hooks/useKpiData';
import { type CurrencyOption } from '@/hooks/useDashboardFilters';

// Mock framer-motion para evitar problemas de animação nos testes
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock do KpiBentoCard
jest.mock('@/components/dashboard/KpiBentoCard', () => {
  return function MockKpiBentoCard({ kpi, onTogglePriority, onRemoveKpi }: any) {
    return (
      <div data-testid={`kpi-card-${kpi.id}`}>
        <h3>{kpi.title}</h3>
        <p>{kpi.description}</p>
        <span data-testid={`value-${kpi.id}`}>{kpi.currentValue}</span>
        <button 
          onClick={() => onTogglePriority(kpi.id)}
          data-testid={`toggle-priority-${kpi.id}`}
        >
          Toggle Priority
        </button>
        {onRemoveKpi && (
          <button 
            onClick={() => onRemoveKpi(kpi.id)}
            data-testid={`remove-${kpi.id}`}
          >
            Remove
          </button>
        )}
      </div>
    );
  };
});

const mockKpis: KpiData[] = [
  {
    id: 'total_volume',
    title: 'Volume Total',
    description: 'Volume total negociado',
    currentValue: 26800000000,
    format: 'currency',
    changePercent: 15.2,
    trend: 'up',
    chartType: 'area',
    chartData: [
      { name: 'Jan', value: 25000000000 },
      { name: 'Feb', value: 26800000000 },
    ],
    isPriority: true,
    order: 1,
    category: 'volume',
  },
  {
    id: 'average_ticket',
    title: 'Ticket Médio',
    description: 'Valor médio por operação',
    currentValue: 1100000,
    format: 'currency',
    changePercent: -2.1,
    trend: 'down',
    chartType: 'line',
    chartData: [
      { name: 'Jan', value: 1120000 },
      { name: 'Feb', value: 1100000 },
    ],
    isPriority: true,
    order: 2,
    category: 'performance',
  },
  {
    id: 'average_spread',
    title: 'Spread Médio',
    description: 'Spread médio das operações',
    currentValue: 459.1,
    format: 'percentage',
    changePercent: 5.3,
    trend: 'up',
    chartType: 'bar',
    chartData: [
      { name: 'Jan', value: 435.2 },
      { name: 'Feb', value: 459.1 },
    ],
    isPriority: true,
    order: 3,
    category: 'performance',
  },
  {
    id: 'conversion_rate',
    title: 'Taxa de Conversão',
    description: 'Taxa de conversão de leads',
    currentValue: 0.25,
    format: 'percentage',
    changePercent: -1.2,
    trend: 'down',
    chartType: 'line',
    chartData: [
      { name: 'Jan', value: 0.253 },
      { name: 'Feb', value: 0.25 },
    ],
    isPriority: true,
    order: 4,
    category: 'performance',
  },
  {
    id: 'retention_rate',
    title: 'Taxa de Retenção',
    description: 'Taxa de retenção de clientes',
    currentValue: 25.33,
    format: 'percentage',
    changePercent: 3.1,
    trend: 'up',
    chartType: 'area',
    chartData: [
      { name: 'Jan', value: 24.6 },
      { name: 'Feb', value: 25.33 },
    ],
    isPriority: true,
    order: 5,
    category: 'performance',
  },
  {
    id: 'gross_margin',
    title: 'Margem Bruta',
    description: 'Margem bruta das operações',
    currentValue: 18.5,
    format: 'percentage',
    changePercent: 2.8,
    trend: 'up',
    chartType: 'bar',
    chartData: [
      { name: 'Jan', value: 18.0 },
      { name: 'Feb', value: 18.5 },
    ],
    isPriority: true,
    order: 6,
    category: 'financial',
  },
];

describe('KpiBentoGrid Component', () => {
  const defaultProps = {
    kpis: mockKpis,
    currency: 'all' as CurrencyOption,
    onTogglePriority: jest.fn(),
    onRemoveKpi: jest.fn(),
    periodData: {
      currentDate: '2025-01-15',
      isSimulated: false,
      periodDescription: 'Current Week',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all KPI cards', () => {
    render(<KpiBentoGrid {...defaultProps} />);

    mockKpis.forEach(kpi => {
      expect(screen.getByTestId(`kpi-card-${kpi.id}`)).toBeInTheDocument();
      expect(screen.getByText(kpi.title)).toBeInTheDocument();
      expect(screen.getByText(kpi.description)).toBeInTheDocument();
    });
  });

  it('should apply correct grid layout classes', () => {
    const { container } = render(<KpiBentoGrid {...defaultProps} />);
    
    const gridContainer = container.querySelector('.grid.grid-cols-12');
    expect(gridContainer).toBeInTheDocument();
    
    // Verifica se os cards têm as classes de grid corretas
    const firstCard = container.querySelector('[data-testid="kpi-card-total_volume"]')?.parentElement;
    expect(firstCard).toHaveClass('col-span-12', 'md:col-span-6', 'row-span-2');
    
    const secondCard = container.querySelector('[data-testid="kpi-card-average_ticket"]')?.parentElement;
    expect(secondCard).toHaveClass('col-span-12', 'md:col-span-6', 'row-span-2');
    
    const thirdCard = container.querySelector('[data-testid="kpi-card-average_spread"]')?.parentElement;
    expect(thirdCard).toHaveClass('col-span-6', 'md:col-span-3', 'row-span-1');
  });

  it('should handle toggle priority correctly', () => {
    render(<KpiBentoGrid {...defaultProps} />);

    const toggleButton = screen.getByTestId('toggle-priority-total_volume');
    fireEvent.click(toggleButton);

    expect(defaultProps.onTogglePriority).toHaveBeenCalledWith('total_volume');
  });

  it('should handle remove KPI when callback is provided', () => {
    render(<KpiBentoGrid {...defaultProps} />);

    const removeButton = screen.getByTestId('remove-total_volume');
    fireEvent.click(removeButton);

    expect(defaultProps.onRemoveKpi).toHaveBeenCalledWith('total_volume');
  });

  it('should not render remove buttons when onRemoveKpi is not provided', () => {
    const propsWithoutRemove = {
      ...defaultProps,
      onRemoveKpi: undefined,
    };

    render(<KpiBentoGrid {...propsWithoutRemove} />);

    expect(screen.queryByTestId('remove-total_volume')).not.toBeInTheDocument();
  });

  it('should render with empty KPI list', () => {
    const emptyProps = {
      ...defaultProps,
      kpis: [],
    };

    const { container } = render(<KpiBentoGrid {...emptyProps} />);
    
    const gridContainer = container.querySelector('.grid.grid-cols-12');
    expect(gridContainer).toBeInTheDocument();
    expect(gridContainer?.children).toHaveLength(0);
  });

  it('should pass correct props to KpiBentoCard components', () => {
    render(<KpiBentoGrid {...defaultProps} />);

    // Verifica se os primeiros dois cards são marcados como large
    const firstCard = screen.getByTestId('kpi-card-total_volume');
    const secondCard = screen.getByTestId('kpi-card-average_ticket');
    const thirdCard = screen.getByTestId('kpi-card-average_spread');

    expect(firstCard).toBeInTheDocument();
    expect(secondCard).toBeInTheDocument();
    expect(thirdCard).toBeInTheDocument();
  });

  it('should handle different currency options', () => {
    const usdProps = {
      ...defaultProps,
      currency: 'usd' as CurrencyOption,
    };

    render(<KpiBentoGrid {...usdProps} />);

    // Verifica se todos os cards ainda são renderizados
    mockKpis.forEach(kpi => {
      expect(screen.getByTestId(`kpi-card-${kpi.id}`)).toBeInTheDocument();
    });
  });

  it('should handle period data correctly', () => {
    const customPeriodProps = {
      ...defaultProps,
      periodData: {
        currentDate: '2025-01-20',
        isSimulated: true,
        periodDescription: 'Simulated Period',
      },
    };

    render(<KpiBentoGrid {...customPeriodProps} />);

    // Verifica se os cards são renderizados com os dados de período corretos
    mockKpis.forEach(kpi => {
      expect(screen.getByTestId(`kpi-card-${kpi.id}`)).toBeInTheDocument();
    });
  });

  it('should maintain responsive grid layout', () => {
    const { container } = render(<KpiBentoGrid {...defaultProps} />);
    
    const gridContainer = container.querySelector('.grid');
    expect(gridContainer).toHaveClass('grid-cols-12');
    expect(gridContainer).toHaveClass('gap-4', 'md:gap-6');
    expect(gridContainer).toHaveClass('auto-rows-[minmax(180px,_1fr)]');
  });

  it('should handle KPI ordering correctly', () => {
    const unorderedKpis = [...mockKpis].reverse();
    const unorderedProps = {
      ...defaultProps,
      kpis: unorderedKpis,
    };

    render(<KpiBentoGrid {...unorderedProps} />);

    // Verifica se todos os KPIs são renderizados independente da ordem
    unorderedKpis.forEach(kpi => {
      expect(screen.getByTestId(`kpi-card-${kpi.id}`)).toBeInTheDocument();
    });
  });
});

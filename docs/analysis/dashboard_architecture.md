
```mermaid
sequenceDiagram
    participant User
    participant FE_Dashboard as "Frontend (Dashboard Page)"
    participant FE_useKpiD<PERSON> as "Frontend (useKpiData Hook)"
    participant BE_<PERSON> as "Backend (API Endpoint)"
    participant BE_KpiService as "Backend (KPI Service)"
    participant BE_C<PERSON> as "Backend (Unified Cache)"
    participant BE_Db as "Backend (Database)"

    User->>FE_Dashboard: Interage com Filtros (ex: muda timeframe)
    FE_Dashboard->>FE_useKpiData: Atualiza estado dos filtros
    
    FE_useKpiData->>FE_useKpiData: Inicia debounce timer
    
    loop Debounce
        FE_useKpiData->>FE_useKpiData: Aguarda 300ms
    end

    FE_useKpiData->>BE_API: GET /api/dashboard/kpis?timeframe=...&currency=...
    BE_API->>BE_KpiService: get_dashboard_kpis(...)

    BE_KpiService->>BE_Cache: GET kpi:dashboard (cache da resposta completa)
    alt Cache Hit
        BE_Cache-->>BE_KpiService: Retorna KPIs cacheados
        BE_KpiService-->>BE_API: Retorna KPIs
        BE_API-->>FE_useKpiData: Resposta JSON com KPIs
        FE_useKpiData->>FE_Dashboard: Atualiza estado com KPIs
        FE_Dashboard->>User: Renderiza KPIs atualizados
    else Cache Miss
        BE_KpiService->>BE_KpiService: Carrega definições de KPIs (JSON)
        
        paralelamente para cada KPI
            BE_KpiService->>BE_Cache: GET kpi:full (cache do KPI individual)
            alt Cache Hit (KPI individual)
                 BE_Cache-->>BE_KpiService: Retorna KPI cacheado
            else Cache Miss (KPI individual)
                BE_KpiService->>BE_Cache: GET kpi:value (cache do valor)
                alt Cache Hit (valor)
                    BE_Cache-->>BE_KpiService: Retorna valor cacheado
                else Cache Miss (valor)
                    BE_KpiService->>BE_Db: Executa query SQL para calcular valor
                    BE_Db-->>BE_KpiService: Retorna resultado
                    BE_KpiService->>BE_Cache: SET kpi:value
                end
                
                BE_KpiService->>BE_Cache: GET kpi:chart (cache do gráfico)
                alt Cache Hit (gráfico)
                    BE_Cache-->>BE_KpiService: Retorna dados do gráfico
                else Cache Miss (gráfico)
                    BE_KpiService->>BE_Db: Executa query SQL para dados históricos
                    BE_Db-->>BE_KpiService: Retorna resultados
                    BE_KpiService->>BE_Cache: SET kpi:chart
                end

                BE_KpiService->>BE_KpiService: Formata KPI completo
                BE_KpiService->>BE_Cache: SET kpi:full
            end
        end
        
        BE_KpiService->>BE_Cache: SET kpi:dashboard (resposta completa)
        BE_KpiService-->>BE_API: Retorna lista de KPIs
        BE_API-->>FE_useKpiData: Resposta JSON com KPIs
        FE_useKpiData->>FE_useKpiData: Filtra apenas os 6 KPIs críticos
        FE_useKpiData->>FE_Dashboard: Atualiza estado com KPIs filtrados
        FE_Dashboard->>User: Renderiza KPIs no Bento Grid
    end
```

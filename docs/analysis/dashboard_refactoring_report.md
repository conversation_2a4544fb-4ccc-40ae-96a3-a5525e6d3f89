
# Relatório de Análise e Refatoração do Dashboard

**Data:** 16/07/2025

## 1. Resumo Executivo

A funcionalidade de dashboard da plataforma é robusta e já implementa conceitos modernos como cache no backend e componentização no frontend. No entanto, a análise revelou uma arquitetura com complexidade acidental, acoplamento entre camadas e manutenção custosa, principalmente devido à construção manual de queries SQL.

Este relatório propõe uma refatoração focada em simplificar o fluxo de dados, modernizar as tecnologias de acesso a dados e gestão de estado, e aumentar a manutenibilidade e performance do sistema.

**Pontos Fortes Atuais:**
- **Cache no Backend:** Uso de um sistema de cache unificado e paralelização para performance.
- **Componentização no Frontend:** Código React bem estruturado em componentes e hooks.
- **UI Moderna:** A interface é limpa e utiliza componentes modernos.

**Principais Recomendações:**
1.  **Adotar um ORM (SQLModel):** Substituir a geração manual de SQL no backend por um ORM para maior segurança, clareza e manutenibilidade.
2.  **Adotar TanStack Query (React Query):** Substituir a lógica manual de fetch e cache no frontend para simplificar o código e melhorar a gestão de estado.
3.  **Consolidar a Lógica do Backend:** Unificar os múltiplos serviços de KPI em uma estrutura mais enxuta e com responsabilidades claras.
4.  **Desacoplar Frontend e Backend:** Criar uma camada de serviço de API no frontend e garantir que a API do backend seja a única fonte da verdade para os dados.

## 2. Arquitetura Atual

A arquitetura atual segue um fluxo de dados que começa na interação do usuário com os filtros do dashboard, dispara uma chamada de API para o backend, que por sua vez orquestra o cálculo de KPIs através de cache e consultas diretas ao banco de dados.

O diagrama de sequência abaixo ilustra este fluxo em detalhe:

```mermaid
sequenceDiagram
    participant User
    participant FE_Dashboard as "Frontend (Dashboard Page)"
    participant FE_useKpiData as "Frontend (useKpiData Hook)"
    participant BE_API as "Backend (API Endpoint)"
    participant BE_KpiService as "Backend (KPI Service)"
    participant BE_Cache as "Backend (Unified Cache)"
    participant BE_Db as "Backend (Database)"

    User->>FE_Dashboard: Interage com Filtros (ex: muda timeframe)
    FE_Dashboard->>FE_useKpiData: Atualiza estado dos filtros
    
    FE_useKpiData->>FE_useKpiData: Inicia debounce timer
    
    loop Debounce
        FE_useKpiData->>FE_useKpiData: Aguarda 300ms
    end

    FE_useKpiData->>BE_API: GET /api/dashboard/kpis?timeframe=...&currency=...
    BE_API->>BE_KpiService: get_dashboard_kpis(...)

    BE_KpiService->>BE_Cache: GET kpi:dashboard (cache da resposta completa)
    alt Cache Hit
        BE_Cache-->>BE_KpiService: Retorna KPIs cacheados
    else Cache Miss
        BE_KpiService->>BE_KpiService: Carrega definições de KPIs (JSON)
        
        paralelamente para cada KPI
            BE_KpiService->>BE_Cache: GET kpi:full (cache do KPI individual)
            alt Cache Hit (KPI individual)
                 BE_Cache-->>BE_KpiService: Retorna KPI cacheado
            else Cache Miss (KPI individual)
                BE_KpiService->>BE_Db: Executa query SQL para calcular valor
                BE_Db-->>BE_KpiService: Retorna resultado
                BE_KpiService->>BE_Cache: SET kpi:value
            end
        end
        
        BE_KpiService->>BE_Cache: SET kpi:dashboard (resposta completa)
        BE_KpiService-->>BE_API: Retorna lista de KPIs
    end
    BE_API-->>FE_useKpiData: Resposta JSON com KPIs
    FE_useKpiData->>FE_useKpiData: Filtra apenas os 6 KPIs críticos
    FE_useKpiData->>FE_Dashboard: Atualiza estado com KPIs filtrados
    FE_Dashboard->>User: Renderiza KPIs no Bento Grid
```

## 3. Análise Crítica e Pontos de Melhoria

- **Geração Manual de SQL:** O arquivo `kpi_calculator.py` contém mais de 500 linhas de código dedicadas a construir queries SQL manualmente. Isso é inflexível, propenso a erros de sintaxe e segurança (SQL Injection), e torna a adição de novos KPIs um processo lento e complexo.
- **Múltiplas Camadas de Serviço no Backend:** A existência de `DashboardService`, `KPICalculator` e `KpiCalculationServiceRefactored` cria confusão sobre as responsabilidades de cada um. A lógica de negócio está espalhada, dificultando o rastreamento de um fluxo de dados.
- **Acoplamento Frontend-Backend:** O frontend (`useKpiData.ts`) faz uma chamada direta à API e depois filtra os dados. A API deveria ser a autoridade e retornar apenas os dados necessários, evitando o tráfego de dados desnecessários e a lógica de filtragem duplicada.
- **Gerenciamento de Estado Manual no Frontend:** O hook `useKpiData.ts` implementa manualmente a lógica de loading, error, refresh e debounce. Ferramentas como o TanStack Query resolvem isso de forma mais elegante e robusta.

## 4. Pesquisa de Mercado e Melhores Práticas

A pesquisa em repositórios open-source populares (como o `full-stack-fastapi-template`) e bibliotecas de UI (como a **Tremor**) revelou um consenso sobre as melhores práticas para aplicações web modernas:

- **Backend:** Uso de ORMs como **SQLAlchemy** e **SQLModel** para abstrair o acesso ao banco de dados. A estrutura de código é geralmente dividida em `Controllers` (API), `Services` (lógica de negócio) e `Repositories` (acesso a dados).
- **Frontend:** Uso de bibliotecas de gestão de estado do servidor como **TanStack Query (React Query)** para simplificar a busca de dados, cache e sincronização. As chamadas de API são encapsuladas em uma camada de serviço para desacoplamento.
- **UI:** Bibliotecas como **Tremor**, **MUI** e **Shadcn UI** fornecem componentes de alta qualidade que aceleram o desenvolvimento e garantem uma UI consistente e moderna.

## 5. Proposta de Arquitetura Refatorada

A arquitetura proposta visa simplificar o sistema, reduzir o acoplamento e melhorar a manutenibilidade.

**Principais Mudanças:**
- **Backend:** O `KpiCalculator` é substituído por um `KpiRepository` que usa **SQLModel** para definir os KPIs e gerar as queries. O `KpiService` é simplificado e se torna o único orquestrador.
- **Frontend:** O `useKpiData` é refatorado para usar o **TanStack Query**. Uma nova camada `src/services/api.ts` é criada para centralizar as chamadas `fetch`.

**Novo Diagrama de Sequência:**

```mermaid
sequenceDiagram
    participant User
    participant FE_Dashboard as "Frontend (Dashboard Page)"
    participant FE_TanStackQuery as "Frontend (TanStack Query)"
    participant FE_ApiService as "Frontend (API Service)"
    participant BE_API as "Backend (API Endpoint)"
    participant BE_KpiService as "Backend (KPI Service)"
    participant BE_KpiRepository as "Backend (KPI Repository com ORM)"
    participant BE_Cache as "Backend (Unified Cache)"
    participant BE_Db as "Backend (Database)"

    User->>FE_Dashboard: Interage com Filtros
    FE_Dashboard->>FE_TanStackQuery: Invalida query de KPIs com novos filtros
    
    FE_TanStackQuery->>FE_ApiService: Chama getKpis(filters)
    FE_ApiService->>BE_API: GET /api/dashboard/kpis?...

    BE_API->>BE_KpiService: get_dashboard_kpis(...)
    BE_KpiService->>BE_Cache: GET (chave com filtros)
    alt Cache Hit
        BE_Cache-->>BE_KpiService: Retorna KPIs cacheados
    else Cache Miss
        BE_KpiService->>BE_KpiRepository: find_kpis_with_filters(...)
        BE_KpiRepository->>BE_Db: Executa query SQL gerada pelo ORM
        BE_Db-->>BE_KpiRepository: Retorna dados
        BE_KpiRepository-->>BE_KpiService: Retorna KPIs formatados
        BE_KpiService->>BE_Cache: SET (chave com filtros)
    end
    
    BE_KpiService-->>BE_API: Retorna lista de KPIs
    BE_API-->>FE_ApiService: Resposta JSON
    FE_ApiService-->>FE_TanStackQuery: Retorna dados
    FE_TanStackQuery->>FE_Dashboard: Atualiza estado com os novos KPIs
    FE_Dashboard->>User: Renderiza KPIs atualizados
```

## 6. Plano de Refatoração Sugerido

1.  **Backend:**
    -   [ ] Adicionar `SQLModel` como dependência.
    -   [ ] Criar um novo `KpiRepository` e definir os modelos de dados e as queries usando SQLModel.
    -   [ ] Refatorar o `KpiService` para usar o `KpiRepository` em vez do `KpiCalculator`.
    -   [ ] Remover o `KpiCalculator` e o `DashboardService` legados.
    -   [ ] Atualizar a API para garantir que ela aceite os filtros e retorne apenas os dados necessários.

2.  **Frontend:**
    -   [ ] Adicionar `@tanstack/react-query` como dependência.
    -   [ ] Criar um `src/services/api.ts` para encapsular as chamadas `fetch`.
    -   [ ] Refatorar o hook `useKpiData.ts` para usar `useQuery` do TanStack Query, removendo a lógica manual.
    -   [ ] Atualizar o componente `Dashboard.tsx` para usar o novo hook.

## 7. Nova Suíte de Testes

Para garantir a segurança durante a refatoração e a qualidade da nova arquitetura, a seguinte suíte de testes foi criada:

-   **`apps/backend/tests/e2e/test_kpi_api_e2e.py`**: Testa o endpoint da API de ponta a ponta.
-   **`apps/backend/tests/integration/test_kpi_service_integration.py`**: Testa a integração do serviço de KPI com suas dependências (cache e banco de dados mockados).
-   **`apps/frontend/src/hooks/__tests__/useKpiData.test.tsx`**: Testa o hook `useKpiData` de forma isolada, mockando a camada de API.
-   **`apps/frontend/tests/dashboard-performance.spec.ts`**: Cria uma linha de base de performance para o carregamento do dashboard, que pode ser usada para comparar o antes e o depois da refatoração.

Estes testes devem ser executados antes, durante e depois da refatoração para garantir que nenhuma funcionalidade foi quebrada e que a performance não foi degradada.

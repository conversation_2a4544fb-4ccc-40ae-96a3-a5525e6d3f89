{"name": "datahero4-monorepo", "version": "1.0.0", "private": true, "description": "DataHero4 Monorepo - Backend API + Frontend Dashboard", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "dev:backend": "turbo run dev --filter=backend", "dev:frontend": "turbo run dev --filter=frontend", "build": "turbo run build", "build:backend": "turbo run build --filter=backend", "build:frontend": "turbo run build --filter=frontend", "start": "turbo run start", "start:backend": "turbo run start --filter=backend", "start:frontend": "turbo run start --filter=frontend", "test": "turbo run test", "test:backend": "turbo run test --filter=backend", "test:frontend": "turbo run test --filter=frontend", "lint": "turbo run lint", "clean": "turbo run clean", "typecheck": "turbo run typecheck", "deploy:backend": "cd apps/backend && poetry run uvicorn main:app --host 0.0.0.0 --port 8000", "deploy:frontend": "cd apps/frontend && npm run preview -- --port 3000 --host 0.0.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20.0.0", "jsdom": "^26.1.0", "turbo": "^1.13.0", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "packageManager": "npm@10.0.0"}
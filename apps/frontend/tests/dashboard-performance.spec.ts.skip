
import { test, expect } from '@playwright/test';

test.describe('Dashboard Performance', () => {
  test('should load critical KPIs within a reasonable time', async ({ page }) => {
    // Navega para a página do dashboard
    const startTime = Date.now();
    await page.goto('/dashboard');

    // Espera pelo título principal para garantir que a página começou a renderizar
    await expect(page.getByRole('heading', { name: 'Métricas Críticas' })).toBeVisible();

    // Espera pelo primeiro card do KPI a ser renderizado.
    // Usamos um seletor que identifica um card de KPI pelo seu conteúdo.
    const firstKpiCard = page.locator('.kpi-bento-card').first();
    await expect(firstKpiCard).toBeVisible({ timeout: 15000 }); // Timeout generoso de 15s

    // Espera que todos os 6 cards de KPI estejam visíveis
    await expect(page.locator('.kpi-bento-card')).toHaveCount(6, { timeout: 15000 });

    const endTime = Date.now();
    const loadTime = endTime - startTime;

    console.log(`Dashboard load time: ${loadTime}ms`);

    // Define um limite de performance aceitável (ex: 5 segundos)
    const PERFORMANCE_THRESHOLD = 5000; // 5 segundos
    expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLD);

    // Adiciona uma asserção para verificar o conteúdo de um KPI específico
    const volumeCard = page.locator('.kpi-bento-card:has-text("Volume Total")');
    await expect(volumeCard.getByTestId('kpi-value')).not.toBeEmpty();
  });
});

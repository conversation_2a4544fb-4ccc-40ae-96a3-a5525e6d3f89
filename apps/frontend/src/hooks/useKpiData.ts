
/**
 * Modern useKpiData Hook with TanStack Query
 * ==========================================
 *
 * Refactored hook using TanStack Query for:
 * - Automatic caching and background updates
 * - Optimistic updates and error handling
 * - Loading states management
 * - Query invalidation and refetching
 */

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import { DashboardApiService } from '@/services/api';
import { KpiData, DashboardFilters, UseKpiDataReturn } from '@/types/kpi';
import { type DashboardFilters as LegacyDashboardFilters } from './useDashboardFilters';

// Re-export types for backward compatibility
export type { KpiData } from '@/types/kpi';
export type { KpiAlert } from '@/lib/api';

/**
 * Query key factory for KPI queries
 */
const kpiQueryKeys = {
  all: ['kpis'] as const,
  dashboard: (filters: DashboardFilters) => ['kpis', 'dashboard', filters] as const,
  single: (kpiId: string, filters: Omit<DashboardFilters, 'priority_only' | 'category'>) =>
    ['kpis', 'single', kpiId, filters] as const,
};

/**
 * Modern useKpiData hook with TanStack Query
 */
export const useKpiData = (filters: LegacyDashboardFilters): UseKpiDataReturn => {
  console.log('useKpiData hook called with filters:', filters);

  const queryClient = useQueryClient();

  // Convert legacy filters to new format
  const queryFilters: DashboardFilters = useMemo(() => ({
    timeframe: filters.timeframe as DashboardFilters['timeframe'],
    currency: filters.currency as DashboardFilters['currency'],
    sector: 'cambio',
    client_id: 'L2M',
    priority_only: true
  }), [filters.timeframe, filters.currency]);

  // Backward compatibility
  const isLoading = isInitialLoading || isFilterChanging || isRefreshing;

  useEffect(() => {
    console.log('🔄 useKpiData - Filter change detected:', filters);

    // Set filter changing state for existing data
    if (kpis.length > 0) {
      setIsFilterChanging(true);
    }

    // Debounce filter changes to avoid excessive API calls
    const debounceTimer = setTimeout(() => {
      loadKpisDebounced();
    }, 300); // 300ms debounce

    return () => clearTimeout(debounceTimer);
  }, [filters]);

  // Load KPIs with filter-aware API calls
  const loadKpisDebounced = async () => {
    console.log('⚡ useKpiData - Loading KPIs with filters:', filters);
    setError(null);

    // Don't change initial loading state if we're just changing filters
    if (!isFilterChanging) {
      setIsInitialLoading(true);
    }

    try {
      // Use full API with filters to get real-time filtered data
      console.log(`🔍 useKpiData - Calling API with filters: timeframe=${filters.timeframe}, currency=${filters.currency}`);
      const response = await getDashboardKpis(
        'cambio',      // sector
        'L2M',         // client_id
        filters.timeframe, // timeframe
        undefined,     // category
        true,          // priority_only
        filters.currency // currency
      );
      console.log('✅ useKpiData - API response received:', response);

      // Define the 6 critical KPIs
      const criticalKpiIds = [
        'total_volume',
        'average_ticket', 
        'average_spread',
        'conversion_rate',
        'retention_rate',
        'gross_margin'
      ];

      // Filter for critical KPIs only
      const criticalKpis = response.kpis
        .filter(kpi => criticalKpiIds.includes(kpi.id))
        .sort((a, b) => {
          // Sort by the order in criticalKpiIds array
          const indexA = criticalKpiIds.indexOf(a.id);
          const indexB = criticalKpiIds.indexOf(b.id);
          return indexA - indexB;
        });

      console.log(`📊 useKpiData - Setting ${criticalKpis.length} filtered KPIs:`, criticalKpis.map(k => k.id));
      
      // Verificar se valores estão sincronizados com filtros
      criticalKpis.forEach(kpi => {
        console.log(`🔍 KPI ${kpi.id} values:`, {
          currentValue: kpi.currentValue,
          changePercent: kpi.changePercent,
          trend: kpi.trend,
          chartDataLength: kpi.chartData?.length || 0
        });
      });
      
      setKpis(criticalKpis);

    } catch (err) {
      console.error('❌ useKpiData - API failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to load KPIs');
      setKpis([]);
    } finally {
      setIsInitialLoading(false);
      setIsFilterChanging(false);
      setIsRefreshing(false);
      console.log('🏁 useKpiData - Loading completed');
    }
  };

  const togglePriority = (kpiId: string) => {
    setKpis(prevKpis =>
      prevKpis.map(kpi =>
        kpi.id === kpiId
          ? { ...kpi, isPriority: !kpi.isPriority }
          : kpi
      )
    );
  };

  const refreshKpis = async () => {
    console.log('🔄 useKpiData - Manual refresh triggered');
    setIsRefreshing(true);
    setError(null);

    try {
      // Use full API to get complete data with charts with current filters
      const response = await getDashboardKpis(
        'cambio',      // sector
        'L2M',         // client_id
        filters.timeframe, // timeframe
        undefined,     // category
        true,          // priority_only
        filters.currency // currency
      );
      
      // Define the 6 critical KPIs
      const criticalKpiIds = [
        'total_volume',
        'average_ticket', 
        'average_spread',
        'conversion_rate',
        'retention_rate',
        'gross_margin'
      ];

      // Filter for critical KPIs only
      const criticalKpis = response.kpis
        .filter(kpi => criticalKpiIds.includes(kpi.id))
        .sort((a, b) => {
          // Sort by the order in criticalKpiIds array
          const indexA = criticalKpiIds.indexOf(a.id);
          const indexB = criticalKpiIds.indexOf(b.id);
          return indexA - indexB;
        });

      setKpis(criticalKpis);
    } catch (err) {
      console.error('❌ useKpiData - Refresh error:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh KPIs');
    } finally {
      setIsRefreshing(false);
      console.log('🏁 useKpiData - Refresh completed');
    }
  };

  return {
    kpis,
    isLoading, // Backward compatibility
    isInitialLoading,
    isFilterChanging,
    isRefreshing,
    error,
    togglePriority,
    refreshKpis,
    snapshotMetadata
  };
};

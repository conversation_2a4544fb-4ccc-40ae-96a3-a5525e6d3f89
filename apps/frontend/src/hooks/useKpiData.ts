import { useState, useEffect } from 'react';
import {
  getDashboardKpis,
  getDashboardSnapshot,
  convertSnapshotToKpiData,
  type KpiData,
  type Kpi<PERSON>lert
} from '@/lib/api';
import { type DashboardFilters } from './useDashboardFilters';

// Re-export types for backward compatibility
export type { KpiData, KpiAlert };

export const useKpiData = (filters: DashboardFilters) => {
  console.log('useKpiData hook called with filters:', filters);

  const [kpis, setKpis] = useState<KpiData[]>([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isFilterChanging, setIsFilterChanging] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [snapshotMetadata, setSnapshotMetadata] = useState<any>(null);

  // Backward compatibility
  const isLoading = isInitialLoading || isFilterChanging || isRefreshing;

  useEffect(() => {
    console.log('🔄 useKpiData - Filter change detected:', filters);

    // Set filter changing state for existing data
    if (kpis.length > 0) {
      setIsFilterChanging(true);
    }

    const fetchKpis = async () => {
      try {
        setError(null);
        
        console.log('📊 Fetching KPIs with filters:', filters);
        
        // Try to get KPIs from the new API first
        let kpiData: KpiData[] = [];
        
        try {
          const response = await getDashboardKpis(
            'cambio',
            'L2M',
            filters.timeframe,
            undefined,
            true,
            filters.currency
          );
          kpiData = response.kpis || [];
          console.log('✅ Got KPIs from new API:', kpiData.length);
        } catch (apiError) {
          console.warn('⚠️ New API failed, trying snapshot fallback:', apiError);
          
          // Fallback to snapshot API
          try {
            const snapshot = await getDashboardSnapshot('L2M');

            if (snapshot?.data) {
              kpiData = convertSnapshotToKpiData(snapshot.data);
              setSnapshotMetadata(snapshot.metadata);
              console.log('✅ Got KPIs from snapshot fallback:', kpiData.length);
            }
          } catch (snapshotError) {
            console.error('❌ Both APIs failed:', { apiError, snapshotError });
            throw apiError; // Throw the original API error
          }
        }

        // Filter to only show priority KPIs (top 6)
        const priorityKpis = kpiData
          .filter(kpi => kpi.isPriority)
          .sort((a, b) => (a.order || 999) - (b.order || 999))
          .slice(0, 6);

        console.log('📊 Final KPIs to display:', priorityKpis.length);
        setKpis(priorityKpis);
        
      } catch (err) {
        console.error('❌ Error fetching KPIs:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch KPIs');
        setKpis([]);
      } finally {
        setIsInitialLoading(false);
        setIsFilterChanging(false);
      }
    };

    fetchKpis();
  }, [filters.timeframe, filters.currency]);

  const refresh = async () => {
    console.log('🔄 Refreshing KPIs...');
    setIsRefreshing(true);
    
    try {
      setError(null);
      
      // Try new API first
      let kpiData: KpiData[] = [];
      
      try {
        const response = await getDashboardKpis(
          'cambio',
          'L2M',
          filters.timeframe,
          undefined,
          true,
          filters.currency
        );
        kpiData = response.kpis || [];
        console.log('✅ Refreshed KPIs from new API:', kpiData.length);
      } catch (apiError) {
        console.warn('⚠️ New API failed during refresh, trying snapshot fallback:', apiError);
        
        // Fallback to snapshot API
        const snapshot = await getDashboardSnapshot('L2M');

        if (snapshot?.data) {
          kpiData = convertSnapshotToKpiData(snapshot.data);
          setSnapshotMetadata(snapshot.metadata);
          console.log('✅ Refreshed KPIs from snapshot fallback:', kpiData.length);
        }
      }

      // Filter to only show priority KPIs (top 6)
      const priorityKpis = kpiData
        .filter(kpi => kpi.isPriority)
        .sort((a, b) => (a.order || 999) - (b.order || 999))
        .slice(0, 6);

      console.log('📊 Refreshed KPIs to display:', priorityKpis.length);
      setKpis(priorityKpis);
      
    } catch (err) {
      console.error('❌ Error refreshing KPIs:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh KPIs');
    } finally {
      setIsRefreshing(false);
    }
  };

  return {
    kpis,
    isLoading,
    isInitialLoading,
    isFilterChanging,
    isRefreshing,
    error,
    refresh,
    snapshotMetadata
  };
};

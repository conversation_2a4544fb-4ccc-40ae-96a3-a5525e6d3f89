import { useQuery } from '@tanstack/react-query';
import { getKpis, KpiFilters } from '@/services/api';
import { KpiData } from '@/types/kpi';

const useKpiData = (filters: KpiFilters) => {
  return useQuery<KpiData[], Error>({
    queryKey: ['kpis', filters],
    queryFn: () => getKpis(filters),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

export default useKpiData;
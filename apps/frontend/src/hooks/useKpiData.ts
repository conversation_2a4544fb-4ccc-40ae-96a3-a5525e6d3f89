/**
 * Modern useKpiData Hook with TanStack Query
 * ==========================================
 * 
 * Refactored hook using TanStack Query for:
 * - Automatic caching and background updates
 * - Optimistic updates and error handling
 * - Loading states management
 * - Query invalidation and refetching
 */

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import { DashboardApiService } from '@/services/api';
import { KpiData, DashboardFilters, UseKpiDataReturn } from '@/types/kpi';
import { type DashboardFilters as LegacyDashboardFilters } from './useDashboardFilters';

// Re-export types for backward compatibility
export type { KpiData } from '@/types/kpi';
export type { KpiAlert } from '@/lib/api';

/**
 * Query key factory for KPI queries
 */
const kpiQueryKeys = {
  all: ['kpis'] as const,
  dashboard: (filters: DashboardFilters) => ['kpis', 'dashboard', filters] as const,
  single: (kpiId: string, filters: Omit<DashboardFilters, 'priority_only' | 'category'>) => 
    ['kpis', 'single', kpiId, filters] as const,
};

/**
 * Modern useKpiData hook with TanStack Query
 */
export const useKpiData = (filters: LegacyDashboardFilters): UseKpiDataReturn => {
  console.log('useKpiData hook called with filters:', filters);
  
  const queryClient = useQueryClient();

  // Convert legacy filters to new format
  const queryFilters: DashboardFilters = useMemo(() => ({
    timeframe: filters.timeframe as DashboardFilters['timeframe'],
    currency: filters.currency as DashboardFilters['currency'],
    sector: 'cambio',
    client_id: 'L2M',
    priority_only: true
  }), [filters.timeframe, filters.currency]);

  // Main KPI query using TanStack Query
  const {
    data: kpis = [],
    isLoading,
    isInitialLoading,
    isFetching,
    error,
    refetch
  } = useQuery({
    queryKey: kpiQueryKeys.dashboard(queryFilters),
    queryFn: () => DashboardApiService.getKpis(queryFilters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: (failureCount, error) => {
      // Don't retry on client errors
      if (error && 'status' in error && (error as any).status >= 400 && (error as any).status < 500) {
        return false;
      }
      return failureCount < 2;
    }
  });

  // Derived loading states
  const isFilterChanging = isFetching && !isInitialLoading;
  const isRefreshing = isFetching && !isInitialLoading && !isFilterChanging;

  // Actions
  const refresh = useCallback(async () => {
    console.log('🔄 Refreshing KPIs...');
    try {
      await DashboardApiService.refreshKpis(queryFilters);
      await queryClient.invalidateQueries({ queryKey: kpiQueryKeys.dashboard(queryFilters) });
    } catch (error) {
      console.error('❌ Error refreshing KPIs:', error);
      throw error;
    }
  }, [queryClient, queryFilters]);

  const togglePriority = useCallback((kpiId: string) => {
    console.log(`🔄 Toggling priority for KPI: ${kpiId}`);
    // This would typically update the KPI priority in the backend
    // For now, just invalidate the query to refetch
    queryClient.invalidateQueries({ queryKey: kpiQueryKeys.dashboard(queryFilters) });
  }, [queryClient, queryFilters]);

  const updateFilters = useCallback((newFilters: Partial<DashboardFilters>) => {
    console.log('🔄 Updating filters:', newFilters);
    // This would typically be handled by the parent component
    // The hook will automatically refetch when filters change
  }, []);

  const calculateSingle = useCallback(async (kpiId: string): Promise<KpiData | null> => {
    console.log(`🔄 Calculating single KPI: ${kpiId}`);
    try {
      const singleKpiFilters = {
        timeframe: queryFilters.timeframe,
        currency: queryFilters.currency,
        sector: queryFilters.sector,
        client_id: queryFilters.client_id
      };
      
      return await DashboardApiService.getSingleKpi(kpiId, singleKpiFilters);
    } catch (error) {
      console.error(`❌ Error calculating single KPI ${kpiId}:`, error);
      throw error;
    }
  }, [queryFilters]);

  // Return hook interface
  return {
    // Data
    kpis,
    filters: queryFilters,
    
    // Loading states
    isLoading: isLoading || isInitialLoading,
    isInitialLoading,
    isRefreshing,
    isFilterChanging,
    error: error as Error | null,
    
    // Actions
    refresh,
    togglePriority,
    updateFilters,
    calculateSingle
  };
};

/**
 * Hook for single KPI calculation
 */
export const useSingleKpi = (
  kpiId: string, 
  filters: Omit<DashboardFilters, 'priority_only' | 'category'>,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: kpiQueryKeys.single(kpiId, filters),
    queryFn: () => DashboardApiService.getSingleKpi(kpiId, filters),
    enabled,
    staleTime: 2 * 60 * 1000, // 2 minutes for single KPIs
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });
};

/**
 * Hook for KPI refresh mutations
 */
export const useKpiRefresh = () => {
  const queryClient = useQueryClient();
  
  return useCallback(async (filters: DashboardFilters) => {
    console.log('🔄 Refreshing all KPIs...');
    
    // Invalidate all KPI queries
    await queryClient.invalidateQueries({ queryKey: kpiQueryKeys.all });
    
    // Force refresh from API
    await DashboardApiService.refreshKpis(filters);
    
    console.log('✅ KPIs refreshed successfully');
  }, [queryClient]);
};

/**
 * Utility hook for KPI query management
 */
export const useKpiQueryUtils = () => {
  const queryClient = useQueryClient();
  
  return {
    // Prefetch KPIs for better UX
    prefetchKpis: useCallback((filters: DashboardFilters) => {
      return queryClient.prefetchQuery({
        queryKey: kpiQueryKeys.dashboard(filters),
        queryFn: () => DashboardApiService.getKpis(filters),
        staleTime: 5 * 60 * 1000
      });
    }, [queryClient]),
    
    // Invalidate specific KPI queries
    invalidateKpis: useCallback((filters?: DashboardFilters) => {
      if (filters) {
        return queryClient.invalidateQueries({ queryKey: kpiQueryKeys.dashboard(filters) });
      } else {
        return queryClient.invalidateQueries({ queryKey: kpiQueryKeys.all });
      }
    }, [queryClient]),
    
    // Get cached KPI data
    getCachedKpis: useCallback((filters: DashboardFilters) => {
      return queryClient.getQueryData<KpiData[]>(kpiQueryKeys.dashboard(filters));
    }, [queryClient])
  };
};

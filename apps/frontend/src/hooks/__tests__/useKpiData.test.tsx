
import { renderHook, waitFor } from '@testing-library/react';
import useKpiData from '../useKpiData';
import * as api from '@/services/api';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock da API
vi.mock('@/services/api');
const mockedApi = api as vi.Mocked<typeof api>;

const mockKpis = [
    { id: 'total_volume', title: 'Volume Total', currentValue: 1000 },
    { id: 'average_ticket', title: 'Ticket Médio', currentValue: 200 },
];

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const wrapper = ({ children }) => (
  <QueryClientProvider client={queryClient}>
    {children}
  </QueryClientProvider>
);

describe('useKpiData', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        queryClient.clear();
    });

    it('should fetch KPIs and return data', async () => {
        mockedApi.getKpis.mockResolvedValue(mockKpis);

        const { result } = renderHook(() => useKpiData({ timeframe: 'week', currency: 'all' }), { wrapper });

        await waitFor(() => {
            expect(result.current.isSuccess).toBe(true);
        });

        expect(result.current.data).toEqual(mockKpis);
        expect(mockedApi.getKpis).toHaveBeenCalledTimes(1);
    });

    it('should handle API error gracefully', async () => {
        const errorMessage = 'Failed to fetch';
        mockedApi.getKpis.mockRejectedValue(new Error(errorMessage));

        const { result } = renderHook(() => useKpiData({ timeframe: 'week', currency: 'all' }), { wrapper });

        await waitFor(() => {
            expect(result.current.isError).toBe(true);
        });

        expect(result.current.error.message).toBe(errorMessage);
    });
});

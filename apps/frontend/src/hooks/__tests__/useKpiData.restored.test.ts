/**
 * Unit tests for restored useKpiData hook
 * Tests the working implementation that uses snapshot API
 */

import { renderHook, waitFor, act } from '@testing-library/react';
import { useKpiData } from '../useKpiData';
import { getDashboardSnapshot, convertSnapshotToKpiData } from '@/lib/api';

// Mock the API module
jest.mock('@/lib/api', () => ({
  getDashboardSnapshot: jest.fn(),
  convertSnapshotToKpiData: jest.fn(),
}));

const mockGetDashboardSnapshot = getDashboardSnapshot as jest.MockedFunction<typeof getDashboardSnapshot>;
const mockConvertSnapshotToKpiData = convertSnapshotToKpiData as jest.MockedFunction<typeof convertSnapshotToKpiData>;

describe('useKpiData (Restored Working Version)', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  const mockFilters = {
    timeframe: 'week' as const,
    currency: 'all' as const
  };

  const mockSnapshotResponse = {
    success: true,
    data: {
      total_volume: {
        id: 'total_volume',
        title: 'Volume Total Negociado',
        description: 'Mede o tamanho da operação',
        currentValue: 5542429196.1,
        format: 'currency',
        trend: 'stable',
        chartType: 'line',
        chartData: [],
        isPriority: true,
        order: 1
      },
      average_ticket: {
        id: 'average_ticket',
        title: 'Ticket Médio',
        description: 'Indica perfil de clientes',
        currentValue: 234084.94,
        format: 'currency',
        trend: 'stable',
        chartType: 'line',
        chartData: [],
        isPriority: true,
        order: 2
      }
    },
    metadata: {
      generated_at: '2025-07-16T14:47:24.241024',
      client_id: 'L2M'
    },
    summary: {}
  };

  const mockKpiData = [
    {
      id: 'total_volume',
      title: 'Volume Total Negociado',
      description: 'Mede o tamanho da operação',
      currentValue: 5542429196.1,
      format: 'currency' as const,
      trend: 'stable' as const,
      chartType: 'line' as const,
      chartData: [],
      isPriority: true,
      order: 1
    },
    {
      id: 'average_ticket',
      title: 'Ticket Médio',
      description: 'Indica perfil de clientes',
      currentValue: 234084.94,
      format: 'currency' as const,
      trend: 'stable' as const,
      chartType: 'line' as const,
      chartData: [],
      isPriority: true,
      order: 2
    }
  ];

  it('should load KPIs successfully using snapshot API', async () => {
    mockGetDashboardSnapshot.mockResolvedValue(mockSnapshotResponse);
    mockConvertSnapshotToKpiData.mockReturnValue(mockKpiData);

    const { result } = renderHook(() => useKpiData(mockFilters));

    // Initially loading
    expect(result.current.isLoading).toBe(true);
    expect(result.current.isInitialLoading).toBe(true);
    expect(result.current.kpis).toEqual([]);

    // Fast-forward debounce timer
    act(() => {
      jest.advanceTimersByTime(300);
    });

    // Wait for API call to complete
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check that snapshot API was called correctly
    expect(mockGetDashboardSnapshot).toHaveBeenCalledWith('L2M');
    expect(mockConvertSnapshotToKpiData).toHaveBeenCalledWith(mockSnapshotResponse);

    // Check final state
    expect(result.current.kpis).toEqual(mockKpiData);
    expect(result.current.error).toBeNull();
    expect(result.current.isInitialLoading).toBe(false);
    expect(result.current.snapshotMetadata).toEqual(mockSnapshotResponse.metadata);
  });

  it('should handle filter changes with debouncing', async () => {
    mockGetDashboardSnapshot.mockResolvedValue(mockSnapshotResponse);
    mockConvertSnapshotToKpiData.mockReturnValue(mockKpiData);

    const { result, rerender } = renderHook(
      ({ filters }) => useKpiData(filters),
      { initialProps: { filters: mockFilters } }
    );

    // Fast-forward initial debounce
    act(() => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Clear mock calls from initial load
    mockGetDashboardSnapshot.mockClear();

    // Change filters
    const newFilters = { timeframe: 'month' as const, currency: 'USD' as const };
    rerender({ filters: newFilters });

    // Should set filter changing state
    expect(result.current.isFilterChanging).toBe(true);

    // Fast-forward debounce timer
    act(() => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should have called API again
    expect(mockGetDashboardSnapshot).toHaveBeenCalledWith('L2M');
    expect(result.current.isFilterChanging).toBe(false);
  });

  it('should handle refresh functionality', async () => {
    mockGetDashboardSnapshot.mockResolvedValue(mockSnapshotResponse);
    mockConvertSnapshotToKpiData.mockReturnValue(mockKpiData);

    const { result } = renderHook(() => useKpiData(mockFilters));

    // Fast-forward initial load
    act(() => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Clear mock calls
    mockGetDashboardSnapshot.mockClear();

    // Call refresh
    act(() => {
      result.current.refreshKpis();
    });

    expect(result.current.isRefreshing).toBe(true);

    await waitFor(() => {
      expect(result.current.isRefreshing).toBe(false);
    });

    // Should have called API again
    expect(mockGetDashboardSnapshot).toHaveBeenCalledWith('L2M');
  });

  it('should handle API errors gracefully', async () => {
    const errorMessage = 'Snapshot API failed';
    mockGetDashboardSnapshot.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useKpiData(mockFilters));

    // Fast-forward debounce
    act(() => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should handle error
    expect(result.current.error).toBe(errorMessage);
    expect(result.current.kpis).toEqual([]);
  });

  it('should filter and sort critical KPIs correctly', async () => {
    const allKpisData = [
      ...mockKpiData,
      {
        id: 'non_critical_kpi',
        title: 'Non Critical KPI',
        description: 'Not in critical list',
        currentValue: 100,
        format: 'number' as const,
        trend: 'stable' as const,
        chartType: 'line' as const,
        chartData: [],
        isPriority: false,
        order: 10
      }
    ];

    mockGetDashboardSnapshot.mockResolvedValue(mockSnapshotResponse);
    mockConvertSnapshotToKpiData.mockReturnValue(allKpisData);

    const { result } = renderHook(() => useKpiData(mockFilters));

    act(() => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should only include critical KPIs
    expect(result.current.kpis).toHaveLength(2);
    expect(result.current.kpis.every(kpi => 
      ['total_volume', 'average_ticket', 'average_spread', 'conversion_rate', 'retention_rate', 'gross_margin'].includes(kpi.id)
    )).toBe(true);
  });

  it('should provide togglePriority functionality', async () => {
    mockGetDashboardSnapshot.mockResolvedValue(mockSnapshotResponse);
    mockConvertSnapshotToKpiData.mockReturnValue(mockKpiData);

    const { result } = renderHook(() => useKpiData(mockFilters));

    act(() => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    const initialKpi = result.current.kpis[0];
    const initialPriority = initialKpi.isPriority;

    // Toggle priority
    act(() => {
      result.current.togglePriority(initialKpi.id);
    });

    // Should have toggled priority
    const updatedKpi = result.current.kpis.find(kpi => kpi.id === initialKpi.id);
    expect(updatedKpi?.isPriority).toBe(!initialPriority);
  });
});

/**
 * Centralized API Service Layer
 * =============================
 * 
 * Modern API service using fetch with proper error handling,
 * type safety, and centralized configuration.
 * Replaces scattered API calls throughout the application.
 */

import { KpiData } from '@/types/kpi';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// Request timeout in milliseconds
const REQUEST_TIMEOUT = 30000;

// API Error Types
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class NetworkError extends Error {
  constructor(message: string = 'Network request failed') {
    super(message);
    this.name = 'NetworkError';
  }
}

export class TimeoutError extends Error {
  constructor(message: string = 'Request timeout') {
    super(message);
    this.name = 'TimeoutError';
  }
}

// Request Options Interface
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
}

// Dashboard Filters Interface
export interface DashboardFilters {
  timeframe: string;
  currency: string;
  sector?: string;
  client_id?: string;
  priority_only?: boolean;
  category?: string;
}

/**
 * Core API client with timeout and error handling
 */
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Make HTTP request with timeout and error handling
   */
  private async request<T>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<T> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = REQUEST_TIMEOUT
    } = options;

    const url = `${this.baseUrl}${endpoint}`;
    
    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: body ? JSON.stringify(body) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData.code
        );
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof ApiError) {
        throw error;
      }

      if (error.name === 'AbortError') {
        throw new TimeoutError(`Request to ${endpoint} timed out after ${timeout}ms`);
      }

      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new NetworkError(`Failed to connect to ${url}`);
      }

      throw new ApiError(`Unexpected error: ${error.message}`);
    }
  }

  /**
   * GET request helper
   */
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const searchParams = params ? new URLSearchParams(params).toString() : '';
    const url = searchParams ? `${endpoint}?${searchParams}` : endpoint;
    
    return this.request<T>(url, { method: 'GET' });
  }

  /**
   * POST request helper
   */
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data
    });
  }
}

// Global API client instance
const apiClient = new ApiClient();

/**
 * Dashboard API Service
 * ====================
 */
export class DashboardApiService {
  /**
   * Get dashboard KPIs with filters
   */
  static async getKpis(filters: DashboardFilters): Promise<KpiData[]> {
    try {
      console.log('🔄 Fetching KPIs with filters:', filters);

      const params = {
        sector: filters.sector || 'cambio',
        client_id: filters.client_id || 'L2M',
        timeframe: filters.timeframe,
        currency: filters.currency,
        priority_only: filters.priority_only ?? true,
        ...(filters.category && { category: filters.category })
      };

      const response = await apiClient.get<{ kpis: KpiData[] }>(
        '/api/dashboard/kpis',
        params
      );

      console.log('✅ KPIs fetched successfully:', response.kpis?.length || 0);
      return response.kpis || [];
    } catch (error) {
      console.error('❌ Error fetching KPIs:', error);
      throw error;
    }
  }

  /**
   * Get single KPI calculation
   */
  static async getSingleKpi(
    kpiId: string,
    filters: Omit<DashboardFilters, 'priority_only' | 'category'>
  ): Promise<KpiData | null> {
    try {
      console.log(`🔄 Fetching single KPI: ${kpiId}`);

      const params = {
        sector: filters.sector || 'cambio',
        client_id: filters.client_id || 'L2M',
        timeframe: filters.timeframe,
        currency: filters.currency
      };

      const response = await apiClient.get<{ kpi: KpiData }>(
        `/api/kpis/${kpiId}/calculate`,
        params
      );

      console.log(`✅ Single KPI ${kpiId} fetched successfully`);
      return response.kpi || null;
    } catch (error) {
      console.error(`❌ Error fetching single KPI ${kpiId}:`, error);
      throw error;
    }
  }

  /**
   * Force refresh KPIs (invalidate cache)
   */
  static async refreshKpis(filters: DashboardFilters): Promise<KpiData[]> {
    try {
      console.log('🔄 Force refreshing KPIs...');

      const params = {
        sector: filters.sector || 'cambio',
        client_id: filters.client_id || 'L2M',
        timeframe: filters.timeframe,
        currency: filters.currency,
        priority_only: filters.priority_only ?? true,
        force_refresh: true,
        ...(filters.category && { category: filters.category })
      };

      const response = await apiClient.get<{ kpis: KpiData[] }>(
        '/api/dashboard/kpis',
        params
      );

      console.log('✅ KPIs force refreshed successfully');
      return response.kpis || [];
    } catch (error) {
      console.error('❌ Error force refreshing KPIs:', error);
      throw error;
    }
  }
}

/**
 * Health Check API
 */
export class HealthApiService {
  /**
   * Check API health status
   */
  static async checkHealth(): Promise<{ status: string; timestamp: string }> {
    try {
      return await apiClient.get('/health');
    } catch (error) {
      console.error('❌ Health check failed:', error);
      throw error;
    }
  }
}

// Export default API client for custom requests
export default apiClient;

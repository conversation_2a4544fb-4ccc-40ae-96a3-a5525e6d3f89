import { KpiData } from '@/types/kpi';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

export interface KpiFilters {
  timeframe: string;
  currency: string;
  clientId?: string;
}

export const getKpis = async (filters: KpiFilters): Promise<KpiData[]> => {
  const params = new URLSearchParams({
    client_id: filters.clientId || 'L2M',
    timeframe: filters.timeframe,
    currency: filters.currency,
  });

  const response = await fetch(`${API_BASE_URL}/api/dashboard/kpis?${params.toString()}`);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Network response was not ok' }));
    throw new Error(errorData.detail || 'Failed to fetch KPIs');
  }

  return response.json();
};
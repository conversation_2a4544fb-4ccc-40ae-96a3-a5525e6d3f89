import { act, renderHook } from '@testing-library/react';
import { useChatStore } from '../chat-store';
import { vi, describe, it, expect, beforeEach } from 'vitest';

// Mock fetch
global.fetch = vi.fn();

const mockFetch = fetch as vi.MockedFunction<typeof fetch>;

describe('useChatStore', () => {
  beforeEach(() => {
    act(() => {
      useChatStore.getState().reset();
    });
    mockFetch.mockClear();
  });

  it('should have correct initial state', () => {
    const { result } = renderHook(() => useChatStore());
    expect(result.current.threads).toEqual([]);
    expect(result.current.currentThreadId).toBeNull();
    expect(result.current.messages).toEqual({});
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should create a thread successfully', async () => {
    const newThread = { id: 'thread-1', title: 'New Thread' };
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ thread: newThread }),
    } as Response);

    const { result } = renderHook(() => useChatStore());

    await act(async () => {
      await result.current.createThread('New Thread');
    });

    expect(result.current.threads).toContainEqual(newThread);
    expect(result.current.currentThreadId).toBe('thread-1');
  });
});
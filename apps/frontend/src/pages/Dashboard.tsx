import React, { useState } from 'react';
import { MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import KpiBentoGrid from '@/components/dashboard/KpiBentoGrid';
import DashboardControls from '@/components/dashboard/DashboardControls';
import AddKpiModal from '@/components/dashboard/AddKpiModal';
import { KPIDrawer } from '@/components/kpi-drawer';
import { DashboardLoading, FilterLoading } from '@/components/dashboard/LoadingStates';
import useKpiData from '@/hooks/useKpiData';
import { useDashboardFilters } from '@/hooks/useDashboardFilters';

const Dashboard = () => {
  const navigate = useNavigate();
  const [showAddKpiModal, setShowAddKpiModal] = useState(false);
  
  const { filters, updateTimeframe, updateCurrency } = useDashboardFilters();
  
  const { data: kpis = [], isLoading, isFetching, error, refetch } = useKpiData(filters);

  const handleExport = () => {
    console.log('Exporting data...');
  };

  const handleAddKpi = () => {
    setShowAddKpiModal(true);
  };

  const handleKpisSelected = (kpiIds: string[]) => {
    console.log(`✅ ${kpiIds.length} KPIs selected:`, kpiIds);
    setShowAddKpiModal(false);
  };

  const handleRemoveKpi = (kpiId: string) => {
    console.log(`🗑️ KPI ${kpiId} will be removed`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-gray-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AI</span>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>Sistema Online</span>
              </div>
              {isFetching && (
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>Atualizando</span>
                </div>
              )}
            </div>
          </div>
          <Button 
            variant="outline" 
            onClick={() => navigate('/')}
            className="flex items-center gap-2 hover:bg-gray-50 transition-colors"
          >
            <MessageSquare className="w-4 h-4" />
            Fazer Perguntas
          </Button>
        </div>
      </div>

      <div className="pb-8">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Métricas Críticas</h1>
            <p className="mt-2 text-gray-600">
              Indicadores essenciais para acompanhamento executivo em tempo real.
            </p>
          </div>

          <DashboardControls 
            filters={filters}
            onTimeframeChange={updateTimeframe}
            onCurrencyChange={updateCurrency}
            onRefresh={() => refetch()}
            onExport={handleExport}
            onAddKpi={handleAddKpi}
            isRefreshing={isFetching}
          />

          {isFetching && !isLoading && (
            <div className="mb-4">
              <FilterLoading />
            </div>
          )}

          {isLoading ? (
            <DashboardLoading />
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                Erro ao carregar KPIs
              </h3>
              <p className="text-red-600 mb-4">{error.message}</p>
              <Button 
                onClick={() => refetch()}
                disabled={isFetching}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isFetching ? 'Carregando...' : 'Tentar novamente'}
              </Button>
            </div>
          ) : (
            <KpiBentoGrid 
              kpis={kpis}
              currency={filters.currency}
              onRemoveKpi={handleRemoveKpi}
              periodData={{
                currentDate: new Date().toISOString(),
                isSimulated: false
              }}
            />
          )}
        </div>
      </div>

      <AddKpiModal
        isOpen={showAddKpiModal}
        onClose={() => setShowAddKpiModal(false)}
        onKpisSelected={handleKpisSelected}
        existingKpiIds={kpis.map(kpi => kpi.id)}
      />
      
      <KPIDrawer />
    </div>
  );
};

export default Dashboard;
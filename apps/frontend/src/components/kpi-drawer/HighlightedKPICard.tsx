import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Props {
  kpiId: string;
  kpiData?: {
    title: string;
    value: string;
    variation: string;
    status: 'positive' | 'negative' | 'neutral';
  };
}

export const HighlightedKPICard: React.FC<Props> = ({ kpiId, kpiData }) => {
  if (!kpiData) {
    return <div>KPI data not available</div>;
  }

  const isPositive = kpiData.variation.startsWith('+');
  const isNegative = kpiData.variation.startsWith('-');
  
  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1.1, opacity: 1 }}
      transition={{ type: 'spring', stiffness: 260, damping: 20 }}
      className="relative"
    >
      <Card className="relative overflow-hidden bg-white shadow-2xl border-0 p-8 max-w-md mx-auto">
        {/* Gradient background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 opacity-60" />
        
        {/* Content */}
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <span className="text-4xl">{kpiData.icon}</span>
            <Badge 
              variant="outline" 
              className={cn(
                "font-bold text-sm px-3 py-1",
                isPositive && "border-green-200 bg-green-50 text-green-700",
                isNegative && "border-red-200 bg-red-50 text-red-700",
                !isPositive && !isNegative && "border-gray-200 bg-gray-50 text-gray-700"
              )}
            >
              <span className="flex items-center gap-1">
                {isPositive && <TrendingUp className="w-3 h-3" />}
                {isNegative && <TrendingDown className="w-3 h-3" />}
                {!isPositive && !isNegative && <Minus className="w-3 h-3" />}
                {kpiData.variation}
              </span>
            </Badge>
          </div>
          
          <h3 className="text-xl font-semibold text-gray-800 mb-3">
            {kpiData.name}
          </h3>
          
          <div className="text-4xl font-bold text-gray-900 mb-4">
            {kpiData.value}
          </div>
          
          <div className="text-sm text-gray-600 leading-relaxed">
            {kpiData.description}
          </div>
        </div>
        
        {/* Decorative gradient border */}
        <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl opacity-10 blur-lg" />
      </Card>
    </motion.div>
  );
};
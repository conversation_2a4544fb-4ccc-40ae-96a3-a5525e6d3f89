import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { motion } from 'framer-motion';
import { Brain, Users, BarChart3, Zap } from 'lucide-react';

interface Props {
  kpiId: string;
}

export const SuperAgentMode: React.FC<Props> = ({ kpiId }) => {
  const insights = [];

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-6"
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center border border-yellow-300">
            <Brain className="w-4 h-4 text-yellow-700" />
          </div>
          <h3 className="ml-3 font-semibold text-gray-900 text-base">Análise Profunda dos Agentes</h3>
        </div>
        <div className="flex items-center gap-2 text-xs text-yellow-700">
          <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
          <span className="font-medium">Tempo real</span>
        </div>
      </div>

      <div className="space-y-4">
        {insights.map((insight, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="p-4 bg-white border border-yellow-200 shadow-sm hover:shadow-md transition-all duration-200 hover:border-yellow-300">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  {insight.icon === '👥' && <Users className="w-4 h-4 text-blue-600" />}
                  {insight.icon === '📊' && <BarChart3 className="w-4 h-4 text-emerald-600" />}
                  <span className="text-xs font-semibold text-gray-800 uppercase tracking-wide">
                    {insight.label}
                  </span>
                </div>
                {insight.type === 'urgent' && (
                  <div className="flex items-center gap-1">
                    <Zap className="w-3 h-3 text-orange-500" />
                    <span className="text-xs text-orange-600 font-medium">Urgente</span>
                  </div>
                )}
              </div>
              
              <p className="text-sm text-gray-700 mb-4 leading-relaxed">
                {insight.text}
              </p>
              
              <div className="flex justify-end">
                <Button 
                  size="sm"
                  variant="outline"
                  className="text-sm border-yellow-300 text-yellow-800 hover:bg-yellow-100 hover:border-yellow-400 transition-colors"
                >
                  {insight.action.label}
                </Button>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-yellow-200 text-xs text-yellow-700 flex items-center justify-center gap-2">
        <Brain className="w-3 h-3" />
        <span>Análise gerada por múltiplos agentes especializados em tempo real</span>
      </div>
    </motion.div>
  );
};
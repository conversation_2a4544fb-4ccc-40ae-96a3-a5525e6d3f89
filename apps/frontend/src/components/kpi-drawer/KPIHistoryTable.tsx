import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Props {
  kpiId: string;
}

export const KPIHistoryTable: React.FC<Props> = ({ kpiId }) => {
  const historyData = [];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'positive': return <TrendingUp className="w-3 h-3" />;
      case 'negative': return <TrendingDown className="w-3 h-3" />;
      default: return <Minus className="w-3 h-3" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'positive': return 'text-green-600 bg-green-50';
      case 'negative': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Histórico Detalhado</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="border-b-0 hover:bg-transparent">
              <TableHead className="border-r-0">Período</TableHead>
              <TableHead className="border-r-0">Valor</TableHead>
              <TableHead className="border-r-0">Variação</TableHead>
              <TableHead className="text-right border-r-0">Tendência</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {historyData.map((item, index) => (
              <TableRow key={index} className="border-b-0 hover:bg-gray-50">
                <TableCell className="font-medium border-r-0">{item.period}</TableCell>
                <TableCell className="border-r-0">
                  <span className="font-semibold">{item.value}</span>
                </TableCell>
                <TableCell className="border-r-0">
                  <span className={cn(
                    "font-medium",
                    item.variation.startsWith('+') && "text-green-600",
                    item.variation.startsWith('-') && "text-red-600",
                    !item.variation.startsWith('+') && !item.variation.startsWith('-') && "text-gray-600"
                  )}>
                    {item.variation}
                  </span>
                </TableCell>
                <TableCell className="text-right border-r-0">
                  <div className={cn(
                    "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                    getStatusColor(item.status)
                  )}>
                    {getStatusIcon(item.status)}
                    <span>
                      {item.status === 'positive' ? 'Alta' :
                       item.status === 'negative' ? 'Baixa' :
                       'Estável'}
                    </span>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
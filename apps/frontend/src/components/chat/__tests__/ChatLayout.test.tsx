
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ChatLayout } from '../ChatLayout';
import { useChatStore } from '@/lib/chat-store';
import { vi, describe, it, expect, beforeEach } from 'vitest';

// Mock the chat store
vi.mock('@/lib/chat-store');

const mockedUseChatStore = useChatStore as vi.MockedFunction<typeof useChatStore>;

// Mock child components
vi.mock('../ChatSidebar', () => ({
  ChatSidebar: ({ isOpen, onToggle }) => (
    <div data-testid="chat-sidebar" className={isOpen ? 'open' : 'closed'}>
      <button onClick={onToggle} data-testid="sidebar-toggle">Toggle</button>
    </div>
  ),
}));

vi.mock('../ChatMain', () => ({
  ChatMain: () => <div data-testid="chat-main"></div>,
}));

const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>{children}</BrowserRouter>
    </QueryClientProvider>
  );
};

describe('ChatLayout', () => {
  beforeEach(() => {
    const mockStore = {
      sidebarOpen: true,
      setSidebarOpen: vi.fn(),
      setCurrentThread: vi.fn(),
      currentThreadId: 'thread-1',
      createThread: vi.fn(),
      isLoading: false,
      isStreaming: false,
      reset: vi.fn(),
    };
    mockedUseChatStore.mockReturnValue(mockStore);
    useChatStore.getState = vi.fn(() => mockStore);
  });

  it('renders sidebar and main area', () => {
    render(<ChatLayout />, { wrapper: TestWrapper });
    expect(screen.getByTestId('chat-sidebar')).toBeInTheDocument();
    expect(screen.getByTestId('chat-main')).toBeInTheDocument();
  });
});

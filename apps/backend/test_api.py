
import pytest
from fastapi.testclient import TestClient
from src.interfaces.api import app

client = TestClient(app)

def test_api_basic_request():
    """Testa a requisição básica para a API."""
    response = client.get("/api/dashboard/kpis", params={
        "client_id": "L2M",
        "timeframe": "week",
        "currency": "all"
    })
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

def test_api_cache_hit():
    """Testa o cache hit da API."""
    params = {
        "client_id": "L2M",
        "timeframe": "week",
        "currency": "all"
    }
    response1 = client.get("/api/dashboard/kpis", params=params)
    assert response1.status_code == 200
    
    response2 = client.get("/api/dashboard/kpis", params=params)
    assert response2.status_code == 200

def test_api_force_refresh():
    """Testa o force refresh da API."""
    response = client.get("/api/dashboard/kpis", params={
        "client_id": "L2M",
        "timeframe": "week",
        "currency": "all",
        "force_refresh": "true"
    })
    assert response.status_code == 200

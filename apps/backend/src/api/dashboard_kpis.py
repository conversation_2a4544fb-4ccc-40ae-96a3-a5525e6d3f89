from typing import Any, Dict, Optional, List
from fastapi import APIRouter, Depends, Query, HTTPException
import logging
import time
from src.services.kpi_service_refactored import get_kpi_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])

@router.get("/kpis", response_model=List[Dict[str, Any]])
async def get_dashboard_kpis(
    client_id: str = Query("L2M", description="Client identifier"),
    timeframe: str = Query("week", description="Time frame: 1d, week, month, quarter"),
    currency: str = Query("all", description="Currency filter: all, usd, eur, gbp"),
    force_refresh: bool = Query(False, description="Force cache refresh")
) -> List[Dict[str, Any]]:
    """
    Obtém KPIs do dashboard com suporte a filtros e cache inteligente.
    """
    start_time = time.time()
    
    kpi_service = get_kpi_service()

    if force_refresh:
        # Invalidation logic can be improved to be more granular
        kpi_service.cache.invalidate("kpi:dashboard", client_id=client_id, timeframe=timeframe, currency=currency)
        kpi_service.cache.invalidate("kpi:full")


    kpis = kpi_service.get_dashboard_kpis(
        client_id=client_id,
        timeframe=timeframe,
        currency=currency
    )
    
    processing_time = round((time.time() - start_time) * 1000, 2)
    logger.info(f"✅ KPI API success - returned {len(kpis)} KPIs in {processing_time}ms")
    
    return kpis
from fastapi import APIRouter, Depends
from typing import Dict, Any
import logging

from src.services.kpi_service_refactored import get_kpi_service

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/monitoring/cache", tags=["monitoring"])
async def get_cache_stats(kpi_service: Any = Depends(get_kpi_service)) -> Dict[str, Any]:
    """
    Endpoint para monitorar as estatísticas do cache de KPIs.
    """
    logger.info("Buscando estatísticas do cache de KPIs")
    return kpi_service.cache.get_stats()
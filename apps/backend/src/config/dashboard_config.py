"""
Dashboard Configuration for DataHero4
===================================

Configurações específicas para o dashboard, incluindo simulação de data
e filtros de KPIs críticos.
"""

import os
from datetime import datetime, date
from typing import List, Optional

from .critical_kpis import CriticalKpisConfig


class DashboardConfig:
    """
    Configuração específica do dashboard com suporte a data simulada
    para compatibilidade com banco de dados estático.
    """
    
    # Real client ID mapping - no mock data
    CLIENT_ID_MAPPING: dict = {
        "L2M": 334,  # Real client ID for L2M
        "default": 334
    }
    
    # Filtros de KPIs
    SHOW_ONLY_CRITICAL_KPIS: bool = os.getenv("SHOW_ONLY_CRITICAL_KPIS", "true").lower() == "true"
    CRITICAL_KPI_IDS: List[str] = CriticalKpisConfig.get_critical_kpi_ids()
    
    # Configurações de período
    DEFAULT_PERIOD_DAYS: int = int(os.getenv("DEFAULT_PERIOD_DAYS", "30"))
    FALLBACK_PERIOD_DAYS: int = int(os.getenv("FALLBACK_PERIOD_DAYS", "90"))
    
    # Configurações de cache
    SNAPSHOT_CACHE_TTL: int = int(os.getenv("SNAPSHOT_CACHE_TTL", "300"))  # 5 minutos
    
    # Configurações de display
    SHOW_PERIOD_INDICATORS: bool = os.getenv("SHOW_PERIOD_INDICATORS", "true").lower() == "true"
    SHOW_DATA_FRESHNESS: bool = os.getenv("SHOW_DATA_FRESHNESS", "true").lower() == "true"

    def get_numeric_client_id(self, client_id: str) -> int:
        """
        Converte client_id string para numeric_client_id apropriado.
        
        Args:
            client_id: String client ID (ex: "L2M")
            
        Returns:
            Numeric client ID para usar nas queries
        """
        if client_id in self.CLIENT_ID_MAPPING:
            return self.CLIENT_ID_MAPPING[client_id]
        
        # Se já for numérico, retorna como int
        try:
            return int(client_id)
        except ValueError:
            # Fallback para cliente default
            return self.CLIENT_ID_MAPPING["default"]
    
    @property
    def current_date(self) -> date:
        """Retorna a data atual real."""
        return date.today()
    
    @property
    def period_start_date(self) -> date:
        """Retorna a data de início do período padrão."""
        current = self.current_date
        return date(current.year, current.month - 1 if current.month > 1 else 12, current.day)
    
    @property
    def period_description(self) -> str:
        """Retorna descrição legível do período atual."""
        current = self.current_date
        return f"Período atual: {current.strftime('%B %Y')}"
    
    def get_filtered_kpi_ids(self, all_kpi_ids: List[str]) -> List[str]:
        """
        Filtra KPIs baseado na configuração.
        Se SHOW_ONLY_CRITICAL_KPIS=True, retorna apenas os críticos.
        """
        if not self.SHOW_ONLY_CRITICAL_KPIS:
            return all_kpi_ids
        
        # Filtra apenas KPIs que estão na lista crítica e existem nos dados
        return [kpi_id for kpi_id in self.CRITICAL_KPI_IDS if kpi_id in all_kpi_ids]
    
    def is_critical_kpi(self, kpi_id: str) -> bool:
        """Verifica se um KPI é crítico."""
        return kpi_id in self.CRITICAL_KPI_IDS


# Instância global da configuração
dashboard_config = DashboardConfig() 
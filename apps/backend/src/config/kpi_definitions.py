"""
Unified KPI Definitions Configuration
====================================

Single source of truth for all KPI definitions, priorities, and configurations.
Replaces multiple scattered configuration files.
"""

from typing import Dict, List, Any
from enum import Enum


class KpiPriority(Enum):
    """KPI Priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class KpiCategory(Enum):
    """KPI Categories."""
    VOLUME = "volume"
    SPREAD = "spread"
    CONVERSION = "conversion"
    RETENTION = "retention"
    COMPLIANCE = "compliance"
    PERFORMANCE = "performance"


# Critical KPIs that must always be calculated first
CRITICAL_KPIS = [
    "total_volume",
    "average_spread", 
    "average_ticket",
    "conversion_rate",
    "retention_rate",
    "compliance_score"
]

# KPI Definitions with unified configuration
KPI_DEFINITIONS = {
    "total_volume": {
        "id": "total_volume",
        "name": "Volume Total Negociado",
        "description": "Volume total de transações processadas",
        "category": KpiCategory.VOLUME,
        "priority": KpiPriority.CRITICAL,
        "unit": "USD",
        "format_type": "currency",
        "chart_type": "area",
        "display_order": 1,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 300,  # 5 minutes
        "thresholds": {
            "warning": 1000000,
            "critical": 500000
        }
    },
    
    "average_spread": {
        "id": "average_spread",
        "name": "Spread Médio",
        "description": "Spread médio das operações de câmbio",
        "category": KpiCategory.SPREAD,
        "priority": KpiPriority.CRITICAL,
        "unit": "bps",
        "format_type": "percentage",
        "chart_type": "line",
        "display_order": 2,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 300,
        "thresholds": {
            "warning": 0.05,
            "critical": 0.10
        }
    },
    
    "average_ticket": {
        "id": "average_ticket",
        "name": "Ticket Médio",
        "description": "Valor médio por transação",
        "category": KpiCategory.VOLUME,
        "priority": KpiPriority.CRITICAL,
        "unit": "USD",
        "format_type": "currency",
        "chart_type": "bar",
        "display_order": 3,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 300,
        "thresholds": {
            "warning": 1000,
            "critical": 500
        }
    },
    
    "conversion_rate": {
        "id": "conversion_rate",
        "name": "Taxa de Conversão",
        "description": "Percentual de leads convertidos em clientes",
        "category": KpiCategory.CONVERSION,
        "priority": KpiPriority.CRITICAL,
        "unit": "%",
        "format_type": "percentage",
        "chart_type": "line",
        "display_order": 4,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 600,  # 10 minutes
        "thresholds": {
            "warning": 0.15,
            "critical": 0.10
        }
    },
    
    "retention_rate": {
        "id": "retention_rate",
        "name": "Taxa de Retenção",
        "description": "Percentual de clientes que retornaram",
        "category": KpiCategory.RETENTION,
        "priority": KpiPriority.CRITICAL,
        "unit": "%",
        "format_type": "percentage",
        "chart_type": "area",
        "display_order": 5,
        "is_priority": True,
        "frequency": "weekly",
        "cache_ttl": 1800,  # 30 minutes
        "thresholds": {
            "warning": 0.70,
            "critical": 0.50
        }
    },
    
    "compliance_score": {
        "id": "compliance_score",
        "name": "Score de Compliance",
        "description": "Pontuação de conformidade regulatória",
        "category": KpiCategory.COMPLIANCE,
        "priority": KpiPriority.CRITICAL,
        "unit": "score",
        "format_type": "number",
        "chart_type": "gauge",
        "display_order": 6,
        "is_priority": True,
        "frequency": "daily",
        "cache_ttl": 900,  # 15 minutes
        "thresholds": {
            "warning": 80,
            "critical": 70
        }
    }
}

# Timeframe configurations
TIMEFRAME_CONFIG = {
    "1d": {
        "name": "Último Dia",
        "sql_filter": "DATE(created_at) = CURRENT_DATE",
        "cache_ttl": 300
    },
    "week": {
        "name": "Última Semana", 
        "sql_filter": "created_at >= CURRENT_DATE - INTERVAL '7 days'",
        "cache_ttl": 600
    },
    "month": {
        "name": "Último Mês",
        "sql_filter": "created_at >= CURRENT_DATE - INTERVAL '30 days'",
        "cache_ttl": 1800
    },
    "quarter": {
        "name": "Último Trimestre",
        "sql_filter": "created_at >= CURRENT_DATE - INTERVAL '90 days'",
        "cache_ttl": 3600
    }
}

# Currency configurations
CURRENCY_CONFIG = {
    "all": {
        "name": "Todas as Moedas",
        "sql_filter": "1=1",
        "symbol": "Multi"
    },
    "usd": {
        "name": "Dólar Americano",
        "sql_filter": "currency = 'USD'",
        "symbol": "$"
    },
    "eur": {
        "name": "Euro",
        "sql_filter": "currency = 'EUR'",
        "symbol": "€"
    },
    "gbp": {
        "name": "Libra Esterlina",
        "sql_filter": "currency = 'GBP'",
        "symbol": "£"
    }
}


def get_kpi_definition(kpi_id: str) -> Dict[str, Any]:
    """Get KPI definition by ID."""
    return KPI_DEFINITIONS.get(kpi_id, {})


def get_critical_kpis() -> List[str]:
    """Get list of critical KPI IDs."""
    return CRITICAL_KPIS.copy()


def get_kpis_by_priority(priority: KpiPriority) -> List[Dict[str, Any]]:
    """Get KPIs filtered by priority level."""
    return [
        kpi for kpi in KPI_DEFINITIONS.values()
        if kpi.get("priority") == priority
    ]


def get_kpis_by_category(category: KpiCategory) -> List[Dict[str, Any]]:
    """Get KPIs filtered by category."""
    return [
        kpi for kpi in KPI_DEFINITIONS.values()
        if kpi.get("category") == category
    ]


def get_timeframe_config(timeframe: str) -> Dict[str, Any]:
    """Get timeframe configuration."""
    return TIMEFRAME_CONFIG.get(timeframe, TIMEFRAME_CONFIG["week"])


def get_currency_config(currency: str) -> Dict[str, Any]:
    """Get currency configuration."""
    return CURRENCY_CONFIG.get(currency, CURRENCY_CONFIG["all"])

"""
Configuração do Intelligent Context Detection System
==================================================

Configurações para controlar o comportamento do sistema inteligente
de detecção de contexto.
"""

import os
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class IntelligentContextConfig:
    """Configuração do sistema de contexto inteligente."""
    
    # Feature flags
    enabled: bool = True
    fallback_to_hardcoded: bool = True
    
    # Performance settings
    cache_enabled: bool = True
    cache_max_size: int = 1000
    cache_ttl_seconds: int = 3600
    max_workers: int = 3
    
    # Complexity thresholds
    simple_confidence_threshold: float = 0.6
    moderate_confidence_threshold: float = 0.7
    complex_confidence_threshold: float = 0.8
    
    # Temporal filtering
    temporal_filter_enabled: bool = True
    temporal_confidence_threshold: float = 0.6  # Reduced from 0.8
    preserve_llm_temporal_entities: bool = True
    
    # LLM settings
    llm_timeout_seconds: int = 15
    llm_max_retries: int = 2
    
    # Railway compatibility
    sync_only: bool = True
    thread_pool_enabled: bool = True
    
    # Debug settings
    debug_logging: bool = False
    performance_logging: bool = True


# Global configuration instance
_config = None

def get_intelligent_context_config() -> IntelligentContextConfig:
    """Obtém configuração global do sistema."""
    global _config
    
    if _config is None:
        _config = IntelligentContextConfig()
        
        # Override with environment variables
        _config.enabled = _get_env_bool("INTELLIGENT_CONTEXT_ENABLED", _config.enabled)
        _config.cache_enabled = _get_env_bool("INTELLIGENT_CONTEXT_CACHE_ENABLED", _config.cache_enabled)
        _config.debug_logging = _get_env_bool("INTELLIGENT_CONTEXT_DEBUG", _config.debug_logging)
        _config.sync_only = _get_env_bool("INTELLIGENT_CONTEXT_SYNC_ONLY", _config.sync_only)
        
        # Performance settings from env
        _config.cache_max_size = _get_env_int("INTELLIGENT_CONTEXT_CACHE_SIZE", _config.cache_max_size)
        _config.max_workers = _get_env_int("INTELLIGENT_CONTEXT_MAX_WORKERS", _config.max_workers)
        
        # Threshold settings from env
        _config.temporal_confidence_threshold = _get_env_float(
            "INTELLIGENT_CONTEXT_TEMPORAL_THRESHOLD", 
            _config.temporal_confidence_threshold
        )
    
    return _config

def _get_env_bool(key: str, default: bool) -> bool:
    """Helper para obter boolean do ambiente."""
    value = os.getenv(key, "").lower()
    if value in ("true", "1", "yes", "on"):
        return True
    elif value in ("false", "0", "no", "off"):
        return False
    return default

def _get_env_int(key: str, default: int) -> int:
    """Helper para obter int do ambiente."""
    try:
        return int(os.getenv(key, str(default)))
    except ValueError:
        return default

def _get_env_float(key: str, default: float) -> float:
    """Helper para obter float do ambiente."""
    try:
        return float(os.getenv(key, str(default)))
    except ValueError:
        return default

def update_config(**kwargs) -> None:
    """Atualiza configuração em runtime."""
    global _config
    config = get_intelligent_context_config()
    
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)

def get_config_summary() -> Dict[str, Any]:
    """Retorna resumo da configuração atual."""
    config = get_intelligent_context_config()
    
    return {
        "system_enabled": config.enabled,
        "railway_compatible": config.sync_only,
        "cache_enabled": config.cache_enabled,
        "cache_size": config.cache_max_size,
        "max_workers": config.max_workers,
        "temporal_threshold": config.temporal_confidence_threshold,
        "debug_mode": config.debug_logging
    }

# Environment-based presets
def configure_for_development():
    """Configuração para desenvolvimento local."""
    update_config(
        debug_logging=True,
        performance_logging=True,
        cache_ttl_seconds=300,  # 5 minutes for faster testing
        max_workers=2  # Less resource usage locally
    )

def configure_for_production():
    """Configuração para produção Railway."""
    update_config(
        debug_logging=False,
        performance_logging=True,
        sync_only=True,  # Critical for Railway
        fallback_to_hardcoded=False,
        cache_enabled=True,
        cache_max_size=2000,  # Larger cache in production
        max_workers=3
    )

def configure_for_testing():
    """Configuração para testes."""
    update_config(
        cache_enabled=False,  # No cache interference in tests
        debug_logging=True,
        llm_timeout_seconds=5,  # Faster timeouts for tests
        max_workers=1  # Single threaded for predictable tests
    )

# Auto-configure based on environment
def auto_configure():
    """Configuração automática baseada no ambiente."""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        configure_for_production()
    elif env == "testing":
        configure_for_testing()
    else:
        configure_for_development()

# Initialize configuration
auto_configure()
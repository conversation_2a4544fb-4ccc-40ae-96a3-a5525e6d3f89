
from typing import Optional
from sqlmodel import Field, SQLModel
from datetime import date, datetime

class Boleta(SQLModel, table=True):
    __tablename__ = 'boleta'
    
    id_boleta: Optional[int] = Field(default=None, primary_key=True)
    id_boleta_status: int
    id_funcionario_criador: int
    id_cliente: int
    id_banco_operacao: Optional[int] = Field(default=None)
    id_parceiro_operacao: Optional[int] = Field(default=None)
    boleta: Optional[str] = Field(default=None)
    observacao: Optional[str] = Field(default=None)
    data_atualizacao: Optional[date] = Field(default=None)
    data_criacao: Optional[date] = Field(default=None)
    status: Optional[int] = Field(default=None)
    cide_aliquota: Optional[float] = Field(default=None)
    cide_valor: Optional[float] = Field(default=None)
    cofins_aliquota: Optional[float] = Field(default=None)
    cofins_valor: Optional[float] = Field(default=None)
    data_entrega_contrato: Optional[date] = Field(default=None)
    data_entrega_me: Optional[date] = Field(default=None)
    data_entrega_mn: Optional[date] = Field(default=None)
    data_envio_banco: Optional[date] = Field(default=None)
    data_recepcao_contrato: Optional[date] = Field(default=None)
    documentacao_vinculada: Optional[str] = Field(default=None)
    iof_cambio_aliquota: Optional[float] = Field(default=None)
    iof_cambio_valor: Optional[float] = Field(default=None)
    irrf_aliquota: Optional[float] = Field(default=None)
    irrf_onus: Optional[str] = Field(default=None)
    irrf_valor: Optional[float] = Field(default=None)
    operacao_real: Optional[bool] = Field(default=None)
    iss_aliquota: Optional[float] = Field(default=None)
    iss_valor: Optional[float] = Field(default=None)
    natureza_cambial: Optional[str] = Field(default=None)
    nr_contrato: Optional[int] = Field(default=None)
    operacao_simultanea: Optional[bool] = Field(default=None)
    operacao_vinculada: Optional[int] = Field(default=None)
    pis_aliquota: Optional[float] = Field(default=None)
    pis_valor: Optional[float] = Field(default=None)
    rde: Optional[str] = Field(default=None)
    taxa_cambio: Optional[float] = Field(default=None)
    tipo_operacao: Optional[str] = Field(default=None)
    valor_me: Optional[float] = Field(default=None)
    valor_mn: Optional[float] = Field(default=None)
    id_cod_pag_rec_exterior: Optional[int] = Field(default=None)
    id_codigo_cliente: Optional[int] = Field(default=None)
    id_codigo_grupo: Optional[int] = Field(default=None)
    id_moeda: Optional[int] = Field(default=None)
    id_natureza_fato: Optional[int] = Field(default=None)
    conta_cliente_entrega_mn: Optional[str] = Field(default=None)
    forma_entrega_mn: Optional[str] = Field(default=None)
    tarifa_bancaria: Optional[float] = Field(default=None)
    data_operacao: Optional[date] = Field(default=None)
    data_taxa_cambio: Optional[date] = Field(default=None)
    iss_manual: Optional[bool] = Field(default=None)
    habilita_iof_email: Optional[bool] = Field(default=None)
    habilita_iss_email: Optional[bool] = Field(default=None)
    habilita_irrf_email: Optional[bool] = Field(default=None)
    habilita_cide_email: Optional[bool] = Field(default=None)
    habilita_pis_email: Optional[bool] = Field(default=None)
    habilita_cofins_email: Optional[bool] = Field(default=None)
    id_cliente_conta: Optional[int] = Field(default=None)
    taxa_base: Optional[float] = Field(default=None)
    id_boleta_relacao_vinculo: Optional[int] = Field(default=None)
    data_previsao_embarque: Optional[date] = Field(default=None)
    ncm: Optional[str] = Field(default=None)
    pis_cofins_manual: Optional[bool] = Field(default=None)
    assinado_digitalmente: Optional[bool] = Field(default=None)
    tarifa_bancaria_old: Optional[str] = Field(default=None)
    id_forma_entrega_mn: Optional[int] = Field(default=None)
    is_operacao_em_reais: Optional[bool] = Field(default=None)

    __table_args__ = {'extend_existing': True}

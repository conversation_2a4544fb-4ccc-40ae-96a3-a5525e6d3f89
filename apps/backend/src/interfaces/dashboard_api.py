"""
Dashboard API Endpoints - DataHero4
===================================

API endpoints for dashboard functionality including KPI data retrieval.
These endpoints integrate with the existing FastAPI application.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from src.services.kpi_service_refactored import KpiServiceRefactored

logger = logging.getLogger(__name__)

# Create router for dashboard endpoints
dashboard_router = APIRouter(prefix="/api", tags=["dashboard"])


# Response models
class KpiAlert(BaseModel):
    """KPI alert configuration."""
    type: str = Field(..., description="Alert type: 'above' or 'below'")
    threshold: float = Field(..., description="Alert threshold value")
    message: Optional[str] = Field(None, description="Alert message")


class KpiData(BaseModel):
    """KPI data response model."""
    id: str = Field(..., description="KPI identifier")
    title: str = Field(..., description="KPI display name")
    description: str = Field(..., description="KPI description")
    currentValue: float = Field(..., description="Current KPI value")
    format: str = Field(..., description="Value format: currency, percentage, number")
    changePercent: Optional[float] = Field(None, description="Percentage change from previous period")
    trend: str = Field(..., description="Trend direction: up, down, stable")
    chartType: str = Field(..., description="Chart type: line, area, bar")
    chartData: List[Dict[str, Any]] = Field(..., description="Chart data points")
    alert: Optional[KpiAlert] = Field(None, description="Alert configuration")
    isPriority: bool = Field(False, description="Whether KPI is priority")
    order: int = Field(0, description="Display order")
    category: str = Field(..., description="KPI category")
    unit: Optional[str] = Field(None, description="Unit of measurement")
    frequency: Optional[str] = Field(None, description="Update frequency")


class DashboardKpisResponse(BaseModel):
    """Response model for dashboard KPIs endpoint."""
    kpis: List[KpiData] = Field(..., description="List of calculated KPIs")
    total_count: int = Field(..., description="Total number of KPIs")
    sector: str = Field(..., description="Business sector")
    client_id: str = Field(..., description="Client identifier")
    timeframe: str = Field(..., description="Time frame")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Response generation timestamp")


class SingleKpiResponse(BaseModel):
    """Response model for single KPI calculation."""
    kpi: KpiData = Field(..., description="Calculated KPI data")
    calculated_at: datetime = Field(default_factory=datetime.utcnow, description="Calculation timestamp")


class DashboardSummaryResponse(BaseModel):
    """Response model for complete dashboard data."""
    kpis: List[KpiData] = Field(..., description="All dashboard KPIs")
    summary: Dict[str, Any] = Field(..., description="Dashboard summary statistics")
    metadata: Dict[str, Any] = Field(..., description="Dashboard metadata")


# Dependency to get KPI service
def get_kpi_service_dependency() -> Any:
    """Dependency to get KPI service instance."""
    return KpiServiceRefactored()


@dashboard_router.get("/dashboard/kpis", response_model=DashboardKpisResponse)
async def get_dashboard_kpis(
    sector: str = Query("cambio", description="Business sector"),
    timeframe: str = Query("1d", description="Time frame for calculations"),
    category: Optional[str] = Query(None, description="Filter by KPI category"),
    priority_only: bool = Query(True, description="Load only priority KPIs for faster response"),
    currency: str = Query("all", description="Currency filter for calculations"),
    kpi_service: Any = Depends(get_kpi_service_dependency)
) -> DashboardKpisResponse:
    """
    Get calculated KPIs for dashboard display.
    
    This endpoint returns a list of calculated KPIs with current values,
    trends, and chart data for dashboard visualization.
    
    **Features:**
    - Lazy loading: KPIs are calculated only when requested
    - Hierarchical caching: Results are cached for performance
    - Real-time data: Values are calculated from current database state
    - Flexible filtering: Filter by category, sector, client
    - Priority loading: Load only critical KPIs first for faster response
    
    **Cache Strategy:**
    - L1 (Memory): 5 minutes
    - L2 (Redis): 1 hour
    - L3 (PostgreSQL): Persistent storage
    
    **Performance:**
    - priority_only=True: ~4-6 KPIs, loads in 10-30 seconds
    - priority_only=False: ~34 KPIs, loads in 3+ minutes
    """
    try:
        logger.info(f"Getting dashboard KPIs for L2M/{sector} (timeframe: {timeframe}, currency: {currency}, priority_only: {priority_only})")

        # Get calculated KPIs from service
        kpis = kpi_service.get_dashboard_kpis(
            sector=sector,
            client_id="L2M",  # Hardcoded - L2M is the DataHero4 client
            timeframe=timeframe,
            category=category,
            priority_only=priority_only,
            currency=currency
        )
        
        # Convert to response models
        kpi_data_list = []
        for kpi in kpis:
            # Convert alert if present
            alert = None
            if kpi.get('alert'):
                alert = KpiAlert(**kpi['alert'])
            
            kpi_data = KpiData(
                id=kpi['id'],
                title=kpi['title'],
                description=kpi['description'],
                currentValue=kpi['currentValue'],
                format=kpi['format'],
                changePercent=kpi.get('changePercent'),
                trend=kpi['trend'],
                chartType=kpi['chartType'],
                chartData=kpi['chartData'],
                alert=alert,
                isPriority=kpi.get('isPriority', False),
                order=kpi.get('order', 0),
                category=kpi['category'],
                unit=kpi.get('unit'),
                frequency=kpi.get('frequency')
            )
            kpi_data_list.append(kpi_data)
        
        response = DashboardKpisResponse(
            kpis=kpi_data_list,
            total_count=len(kpi_data_list),
            sector=sector,
            client_id=client_id,
            timeframe=timeframe
        )
        
        logger.info(f"Returning {len(kpi_data_list)} KPIs for dashboard (priority_only: {priority_only})")
        return response
        
    except Exception as e:
        logger.error(f"Error in get_dashboard_kpis: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve dashboard KPIs: {str(e)}"
        )


@dashboard_router.get("/kpis/{kpi_id}/calculate", response_model=SingleKpiResponse)
async def calculate_kpi(
    kpi_id: str,
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query("L2M", description="Client identifier"),
    kpi_service: Any = Depends(get_kpi_service_dependency)
) -> SingleKpiResponse:
    """
    Calculate a specific KPI on demand.
    
    This endpoint calculates and returns data for a single KPI.
    Useful for lazy loading scenarios where only specific KPIs
    are needed.
    
    **Use Cases:**
    - Lazy loading: Calculate KPI only when user views it
    - Real-time updates: Get fresh calculation for specific KPI
    - Detailed analysis: Focus on single KPI performance
    """
    try:
        logger.info(f"Calculating KPI {kpi_id} for {client_id}/{sector}")
        
        # Calculate single KPI
        kpi_data = kpi_service.calculate_single_kpi(
            kpi_id=kpi_id,
            sector=sector,
            client_id=client_id
        )
        
        if not kpi_data:
            raise HTTPException(
                status_code=404,
                detail=f"KPI '{kpi_id}' not found or inactive"
            )
        
        # Convert alert if present
        alert = None
        if kpi_data.get('alert'):
            alert = KpiAlert(**kpi_data['alert'])
        
        kpi_response = KpiData(
            id=kpi_data['id'],
            title=kpi_data['title'],
            description=kpi_data['description'],
            currentValue=kpi_data['currentValue'],
            format=kpi_data['format'],
            changePercent=kpi_data.get('changePercent'),
            trend=kpi_data['trend'],
            chartType=kpi_data['chartType'],
            chartData=kpi_data['chartData'],
            alert=alert,
            isPriority=kpi_data.get('isPriority', False),
            order=kpi_data.get('order', 0),
            category=kpi_data['category'],
            unit=kpi_data.get('unit'),
            frequency=kpi_data.get('frequency')
        )
        
        response = SingleKpiResponse(kpi=kpi_response)
        
        logger.info(f"Successfully calculated KPI {kpi_id}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating KPI {kpi_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to calculate KPI '{kpi_id}': {str(e)}"
        )


@dashboard_router.get("/kpis/available")
async def get_available_kpis(
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query("L2M", description="Client identifier"),
    kpi_service: Any = Depends(get_kpi_service_dependency)
):
    """
    Get list of available KPIs for selection.

    Returns KPI definitions without calculating values for fast loading.
    Perfect for KPI selector interfaces.
    """
    try:
        logger.info(f"📋 Getting available KPIs for {client_id}/{sector}")

        # Get KPI definitions only (no calculations)
        kpi_definitions = kpi_service.get_kpi_definitions(sector=sector)

        # Format for frontend selection
        available_kpis = []
        for kpi_def in kpi_definitions:
            available_kpis.append({
                'id': kpi_def['id'],
                'name': kpi_def['name'],
                'description': kpi_def['description'],
                'category': kpi_def['category'],
                'isPriority': kpi_def['is_priority'],
                'format': kpi_def['format_type'],
                'unit': kpi_def['unit'],
                'frequency': kpi_def['frequency']
            })

        return {
            'kpis': available_kpis,
            'total_count': len(available_kpis),
            'sector': sector,
            'client_id': client_id
        }

    except Exception as e:
        logger.error(f"Error getting available KPIs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get available KPIs: {str(e)}")


@dashboard_router.get("/dashboard", response_model=DashboardSummaryResponse)
async def get_dashboard_summary(
    sector: str = Query("cambio", description="Business sector"),
    client_id: str = Query("L2M", description="Client identifier"),
    timeframe: str = Query("1d", description="Time frame for calculations"),
    kpi_service: Any = Depends(get_kpi_service_dependency)
) -> DashboardSummaryResponse:
    """
    Get complete dashboard data including KPIs and summary statistics.
    
    This endpoint provides a comprehensive view of the dashboard
    including all KPIs, summary statistics, and metadata.
    
    **Includes:**
    - All active KPIs with calculated values
    - Summary statistics (totals, averages, trends)
    - Dashboard metadata (last update, data freshness)
    """
    try:
        logger.info(f"Getting complete dashboard for {client_id}/{sector}")
        
        # Get all KPIs
        kpis = kpi_service.get_dashboard_kpis(
            sector=sector,
            client_id=client_id,
            timeframe=timeframe
        )
        
        # Convert to response models
        kpi_data_list = []
        priority_count = 0
        total_value = 0
        
        for kpi in kpis:
            # Convert alert if present
            alert = None
            if kpi.get('alert'):
                alert = KpiAlert(**kpi['alert'])
            
            kpi_data = KpiData(
                id=kpi['id'],
                title=kpi['title'],
                description=kpi['description'],
                currentValue=kpi['currentValue'],
                format=kpi['format'],
                changePercent=kpi.get('changePercent'),
                trend=kpi['trend'],
                chartType=kpi['chartType'],
                chartData=kpi['chartData'],
                alert=alert,
                isPriority=kpi.get('isPriority', False),
                order=kpi.get('order', 0),
                category=kpi['category'],
                unit=kpi.get('unit'),
                frequency=kpi.get('frequency')
            )
            kpi_data_list.append(kpi_data)
            
            if kpi.get('isPriority'):
                priority_count += 1
            
            # Sum currency values for total
            if kpi['format'] == 'currency':
                total_value += kpi['currentValue']
        
        # Generate summary statistics
        summary = {
            'total_kpis': len(kpi_data_list),
            'priority_kpis': priority_count,
            'total_value': total_value,
            'categories': list(set(kpi['category'] for kpi in kpis)),
            'last_calculation': datetime.utcnow().isoformat()
        }
        
        # Generate metadata
        metadata = {
            'sector': sector,
            'client_id': client_id,
            'timeframe': timeframe,
            'data_freshness': 'real-time',
            'cache_enabled': True,
            'version': '1.0'
        }
        
        response = DashboardSummaryResponse(
            kpis=kpi_data_list,
            summary=summary,
            metadata=metadata
        )
        
        logger.info(f"Returning complete dashboard with {len(kpi_data_list)} KPIs")
        return response
        
    except Exception as e:
        logger.error(f"Error in get_dashboard_summary: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve dashboard summary: {str(e)}"
        )

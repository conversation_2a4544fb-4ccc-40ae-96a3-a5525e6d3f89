import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from src.services.kpi_service_refactored import get_kpi_service

logger = logging.getLogger(__name__)

dashboard_router = APIRouter(prefix="/api", tags=["dashboard"])

class KpiData(BaseModel):
    id: str
    title: str
    currentValue: float

@dashboard_router.get("/dashboard/kpis", response_model=List[KpiData])
async def get_dashboard_kpis(
    client_id: str = Query("L2M", description="Client identifier"),
    timeframe: str = Query("week", description="Time frame for calculations"),
    currency: str = Query("all", description="Currency filter for calculations"),
    kpi_service: Any = Depends(get_kpi_service)
) -> List[KpiData]:
    try:
        logger.info(f"Getting dashboard KPIs for {client_id} (timeframe: {timeframe}, currency: {currency})")
        
        kpis = kpi_service.get_dashboard_kpis(
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        return kpis
        
    except Exception as e:
        logger.error(f"Error in get_dashboard_kpis: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve dashboard KPIs: {str(e)}"
        )
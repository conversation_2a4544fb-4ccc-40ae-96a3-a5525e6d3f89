from typing import List, Dict, Any
from sqlmodel import Session, select, func
from datetime import date, timedelta
from src.models.orm_models import <PERSON><PERSON><PERSON>
from sqlalchemy.sql import case

class KpiRepository:
    def __init__(self, session: Session):
        self.session = session

    def _get_period_filter(self, start_date: date, end_date: date):
        return [Boleta.data_operacao >= start_date, Boleta.data_operacao <= end_date]

    def get_total_volume(self, client_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        filters = self._get_period_filter(start_date, end_date)
        statement = select(
            func.sum(Boleta.valor_me).label("total_volume"),
            func.count().label("total_operations"),
            func.avg(Boleta.valor_me).label("avg_volume")
        ).where(Boleta.id_cliente == client_id, *filters)
        
        result = self.session.execute(statement).first()
        return result._asdict() if result and result.total_volume is not None else {"total_volume": 0.0, "total_operations": 0, "avg_volume": 0.0}

    def get_average_spread(self, client_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        filters = self._get_period_filter(start_date, end_date)
        
        spread_expression = case(
            (Boleta.tipo_operacao == 'VENDA', ((Boleta.taxa_cambio - Boleta.taxa_base) / Boleta.taxa_base) * 100),
            (Boleta.tipo_operacao == 'COMPRA', ((Boleta.taxa_base - Boleta.taxa_cambio) / Boleta.taxa_base) * 100),
            else_=0
        )

        statement = select(
            func.avg(spread_expression).label("average_spread"),
            func.count().label("total_operations")
        ).where(
            Boleta.id_cliente == client_id,
            Boleta.taxa_cambio != None,
            Boleta.taxa_base != None,
            Boleta.taxa_base > 0,
            *filters
        )
        
        result = self.session.execute(statement).first()
        return result._asdict() if result and result.average_spread is not None else {"average_spread": 0.0, "total_operations": 0}

    def get_average_ticket(self, client_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        filters = self._get_period_filter(start_date, end_date)
        statement = select(
            func.avg(Boleta.valor_me).label("average_ticket"),
            (func.sum(Boleta.valor_me) / func.count()).label("calculated_avg"),
            func.count().label("total_operations")
        ).where(Boleta.id_cliente == client_id, *filters)
        
        result = self.session.execute(statement).first()
        return result._asdict() if result and result.average_ticket is not None else {"average_ticket": 0.0, "calculated_avg": 0.0, "total_operations": 0}

    def get_growth_percentage(self, client_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        previous_end_date = start_date - timedelta(days=1)
        previous_start_date = previous_end_date - (end_date - start_date)

        current_filters = self._get_period_filter(start_date, end_date)
        previous_filters = self._get_period_filter(previous_start_date, previous_end_date)

        current_volume_stmt = select(func.sum(Boleta.valor_me)).where(Boleta.id_cliente == client_id, *current_filters).scalar_subquery()
        previous_volume_stmt = select(func.sum(Boleta.valor_me)).where(Boleta.id_cliente == client_id, *previous_filters).scalar_subquery()

        statement = select(
            current_volume_stmt.label("current_volume"),
            previous_volume_stmt.label("previous_volume"),
            ((current_volume_stmt - previous_volume_stmt) / func.nullif(previous_volume_stmt, 0) * 100).label("growth_percentage")
        )

        result = self.session.execute(statement).first()
        return result._asdict() if result and result.growth_percentage is not None else {"current_volume": 0.0, "previous_volume": 0.0, "growth_percentage": 0.0}

    def get_volume_by_currency(self, client_id: int, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        return []

    def get_gross_margin(self, client_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        return {"gross_margin": 0.0}

    def get_net_margin(self, client_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        return {"net_margin": 0.0}

    def get_operations_roi(self, client_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        return {"operations_roi": 0.0}
"""
Modern KPI Service with SQLModel Repository
===========================================

Clean, modern KPI service using:
- SQLModel ORM for type-safe queries
- Repository pattern for data access
- Unified cache system
- Clean separation of concerns
"""

import logging
import os
from typing import Dict, List, Any, Optional
from sqlmodel import Session, create_engine
from contextlib import contextmanager

from src.caching.unified_cache_system import get_unified_cache
from src.repositories.kpi_repository import KpiRepository
from src.config.kpi_definitions import get_kpi_definition, CRITICAL_KPIS

logger = logging.getLogger(__name__)


class KpiService:
    """Modern KPI service with repository pattern."""
    
    def __init__(self):
        self.cache = get_unified_cache()
        self.engine = self._create_engine()
        logger.info("✅ Modern KPI Service initialized")

    def _create_engine(self):
        """Create SQLAlchemy engine from environment variables."""
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            # Fallback for development
            database_url = "postgresql://postgres:password@localhost:5432/datahero4"
            logger.warning("Using fallback database URL for development")

        return create_engine(database_url, echo=False)
    
    @contextmanager
    def get_repository(self):
        """Get KPI repository with database session."""
        try:
            with Session(self.engine) as session:
                yield KpiRepository(session)
        except Exception as e:
            logger.error(f"Error creating repository: {e}")
            raise
    
    def get_dashboard_kpis(
        self,
        sector: str = "cambio",
        timeframe: str = "week",
        currency: str = "all",
        priority_only: bool = True,
        category: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get dashboard KPIs with caching.

        Args:
            sector: Business sector (cambio)
            timeframe: Time period for calculations
            currency: Currency filter
            priority_only: Return only priority KPIs
            category: Optional category filter

        Returns:
            List of calculated KPI data
        """
        try:
            # Check cache first
            cache_key = f"dashboard_kpis:{sector}:{timeframe}:{currency}:{priority_only}:{category}"
            cached_result = self.cache.get(cache_key)

            if cached_result is not None:
                logger.info(f"✅ Dashboard KPIs from cache: {len(cached_result)} KPIs")
                return cached_result

            # Calculate KPIs using repository
            with self.get_repository() as repo:
                # Get KPIs from repository - no client validation needed
                # We analyze L2M's business data directly

                kpis = repo.get_dashboard_kpis(
                    sector=sector,
                    timeframe=timeframe,
                    currency=currency,
                    priority_only=priority_only,
                    category=category
                )
                
                # Cache result with appropriate TTL
                cache_ttl = self._get_cache_ttl(timeframe)
                self.cache.set(cache_key, kpis, ttl=cache_ttl)
                
                logger.info(f"✅ Calculated {len(kpis)} KPIs and cached for {cache_ttl}s")
                return kpis
                
        except Exception as e:
            logger.error(f"Error getting dashboard KPIs: {e}")
            raise
    
    def calculate_single_kpi(
        self,
        kpi_id: str,
        sector: str = "cambio",
        timeframe: str = "week",
        currency: str = "all"
    ) -> Optional[Dict[str, Any]]:
        """
        Calculate a single KPI on demand.

        Args:
            kpi_id: KPI identifier
            sector: Business sector (cambio)
            timeframe: Time period
            currency: Currency filter

        Returns:
            Single KPI data or None if not found
        """
        try:
            # Check cache first
            cache_key = f"single_kpi:{kpi_id}:{sector}:{timeframe}:{currency}"
            cached_result = self.cache.get(cache_key)

            if cached_result is not None:
                logger.info(f"✅ Single KPI {kpi_id} from cache")
                return cached_result

            # Calculate using repository
            with self.get_repository() as repo:
                # Calculate KPI directly - no client validation needed

                kpi_data = repo.calculate_single_kpi(
                    kpi_id=kpi_id,
                    timeframe=timeframe,
                    currency=currency
                )
                
                if kpi_data:
                    # Cache with KPI-specific TTL
                    kpi_def = get_kpi_definition(kpi_id)
                    cache_ttl = kpi_def.get('cache_ttl', 300)
                    self.cache.set(cache_key, kpi_data, ttl=cache_ttl)
                    
                    logger.info(f"✅ Calculated single KPI {kpi_id}")
                
                return kpi_data
                
        except Exception as e:
            logger.error(f"Error calculating single KPI {kpi_id}: {e}")
            raise
    
    def invalidate_kpis(
        self,
        kpi_ids: Optional[List[str]] = None,
        client_id: Optional[str] = None,
        timeframe: Optional[str] = None,
        currency: Optional[str] = None
    ):
        """
        Invalidate KPI cache selectively.
        
        Args:
            kpi_ids: List of KPI IDs to invalidate
            client_id: Client to invalidate
            timeframe: Timeframe to invalidate
            currency: Currency to invalidate
        """
        try:
            if kpi_ids is None:
                kpi_ids = CRITICAL_KPIS
            
            patterns_to_invalidate = []
            
            # Dashboard KPIs patterns
            if client_id and timeframe and currency:
                patterns_to_invalidate.append(
                    f"dashboard_kpis:{client_id}:*:{timeframe}:{currency}:*"
                )
            
            # Single KPI patterns
            for kpi_id in kpi_ids:
                if client_id:
                    patterns_to_invalidate.append(
                        f"single_kpi:{kpi_id}:{client_id}:*"
                    )
                else:
                    patterns_to_invalidate.append(
                        f"single_kpi:{kpi_id}:*"
                    )
            
            # Invalidate cache patterns
            for pattern in patterns_to_invalidate:
                self.cache.invalidate_pattern(pattern)
            
            logger.info(f"🔄 Invalidated cache for {len(kpi_ids)} KPIs")
            
        except Exception as e:
            logger.error(f"Error invalidating KPI cache: {e}")
    
    def get_kpi_metadata(self, kpi_id: str) -> Optional[Dict[str, Any]]:
        """Get KPI metadata from configuration."""
        return get_kpi_definition(kpi_id)
    
    def get_available_kpis(self, priority_only: bool = False) -> List[Dict[str, Any]]:
        """Get list of available KPIs."""
        if priority_only:
            return [get_kpi_definition(kpi_id) for kpi_id in CRITICAL_KPIS]
        
        from src.config.kpi_definitions import KPI_DEFINITIONS
        return list(KPI_DEFINITIONS.values())
    
    def _get_cache_ttl(self, timeframe: str) -> int:
        """Get appropriate cache TTL based on timeframe."""
        ttl_map = {
            "1d": 300,      # 5 minutes for daily data
            "week": 600,    # 10 minutes for weekly data
            "month": 1800,  # 30 minutes for monthly data
            "quarter": 3600 # 1 hour for quarterly data
        }
        return ttl_map.get(timeframe, 600)


# Singleton instance
_kpi_service_instance = None


def get_kpi_service() -> KpiService:
    """Get singleton KPI service instance."""
    global _kpi_service_instance
    if _kpi_service_instance is None:
        _kpi_service_instance = KpiService()
    return _kpi_service_instance


# Backward compatibility
def get_kpi_service_refactored() -> KpiService:
    """Backward compatibility alias."""
    return get_kpi_service()

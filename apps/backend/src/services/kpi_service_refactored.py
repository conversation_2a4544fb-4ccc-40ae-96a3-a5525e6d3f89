"""
KPI Service Refatorado - DataHero4
==================================

Versão refatorada do serviço de KPIs usando o sistema de cache unificado.
Remove todas as implementações de cache legadas.
"""

import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
try:
    from sqlalchemy import text
except ImportError:
    # Fallback se sqlalchemy não estiver disponível
    text = lambda x: x

from src.models.learning_models import KpiDefinition
from src.utils.learning_db_utils import get_db_manager
from src.caching.unified_cache_system import get_unified_cache
from src.services.kpi_query_manager_json import KpiQueryManagerJSON

logger = logging.getLogger(__name__)


class KpiCalculationServiceRefactored:
    """Serviço refatorado para cálculo e formatação de KPIs."""
    
    def __init__(self):
        # Cache unificado
        self.cache = get_unified_cache()

        # Database manager
        self.db_manager = None
        self._init_db_connection()

        # Query manager
        self.query_manager = KpiQueryManagerJSON(sector="cambio")

        # Paralelização
        self.executor = ThreadPoolExecutor(max_workers=6)
        self._cache_lock = threading.Lock()

        logger.info("✅ KPI Service Refatorado inicializado com cache unificado e paralelização")
    
    def _init_db_connection(self):
        """Inicializa conexão com banco de dados."""
        try:
            self.db_manager = get_db_manager()
            logger.info("✅ KPI Service: Usando learning database manager")
        except Exception as e:
            logger.error(f"❌ Failed to initialize database connection: {e}")
            # Fail fast - database connection is required
            raise ValueError(f"Database connection required for KPI service: {e}")
    
    def get_dashboard_kpis(
        self,
        sector: str = "cambio",
        client_id: str = "L2M", 
        timeframe: str = "week",
        category: Optional[str] = None,
        priority_only: bool = True,
        currency: str = "all"
    ) -> List[Dict[str, Any]]:
        """
        Retorna KPIs formatados para o dashboard com suporte a filtros.
        
        Args:
            sector: Setor de negócio
            client_id: ID do cliente
            timeframe: Período ('1d', 'week', 'month', 'quarter')
            category: Categoria específica (opcional)
            priority_only: Apenas KPIs prioritários
            currency: Filtro de moeda ('all', 'usd', 'eur', 'gbp')
            
        Returns:
            Lista de KPIs formatados
        """
        logger.info(f"📊 Calculando KPIs - timeframe: {timeframe}, currency: {currency}")
        
        # Tentar cache primeiro
        cached_result = self.cache.get(
            "kpi:dashboard",
            client_id=client_id,
            timeframe=timeframe,
            category=category,
            priority_only=priority_only,
            currency=currency
        )
        
        if cached_result is not None:
            logger.info(f"✅ Dashboard KPIs do cache: {len(cached_result)} KPIs")
            return cached_result
        
        # Calcular KPIs
        kpis = []
        
        # Carregar definições de KPIs
        kpi_definitions = self._load_kpi_definitions(
            sector=sector,
            category=category,
            priority_only=priority_only
        )
        
        # Calcular KPIs em paralelo para melhor performance
        kpis = self._calculate_kpis_parallel(
            kpi_definitions, client_id, timeframe, currency
        )
        
        # Cachear resultado
        self.cache.set(
            "kpi:dashboard",
            kpis,
            timeframe=timeframe,
            client_id=client_id,
            category=category,
            priority_only=priority_only,
            currency=currency
        )
        
        logger.info(f"✅ Calculados {len(kpis)} KPIs com sucesso")
        return kpis

    def _calculate_kpis_parallel(
        self,
        kpi_definitions: List[Dict[str, Any]],
        client_id: str,
        timeframe: str,
        currency: str
    ) -> List[Dict[str, Any]]:
        """
        Calcula múltiplos KPIs em paralelo usando ThreadPoolExecutor.
        """
        start_time = time.time()
        results = []
        future_to_kpi = {}

        # Submeter todas as tarefas
        for kpi_def in kpi_definitions:
            future = self.executor.submit(
                self._calculate_single_kpi_thread_safe,
                kpi_def, client_id, timeframe, currency
            )
            future_to_kpi[future] = kpi_def

        # Coletar resultados conforme completam
        for future in as_completed(future_to_kpi):
            kpi_def = future_to_kpi[future]
            try:
                result = future.result(timeout=10)  # Timeout de 10s por KPI
                if result:
                    results.append(result)
                else:
                    logger.warning(f"⚠️ KPI {kpi_def.get('id')} returned None")
            except Exception as e:
                logger.error(f"❌ Error calculating KPI {kpi_def.get('id')}: {e}")
                # Fail fast - re-raise the exception to expose the issue
                raise

        parallel_time = (time.time() - start_time) * 1000
        logger.info(f"🚀 Calculated {len(results)} KPIs in parallel in {parallel_time:.0f}ms")

        return results

    def _calculate_single_kpi_thread_safe(
        self,
        kpi_def: Dict[str, Any],
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[Dict[str, Any]]:
        """
        Versão thread-safe do cálculo de KPI individual.
        """
        kpi_id = kpi_def.get('id', 'unknown')

        # Check cache com lock
        cache_key = f"kpi:full:{kpi_id}:{client_id}:{timeframe}:{currency}"

        with self._cache_lock:
            cached_result = self.cache.get(
                "kpi:full",
                kpi_id=kpi_id,
                client_id=client_id,
                timeframe=timeframe,
                currency=currency
            )
            if cached_result:
                logger.info(f"🎯 Cache hit for {kpi_id}")
                return cached_result

        # Calcular se não estiver em cache
        start_time = time.time()

        try:
            result = self._calculate_single_kpi(kpi_def, client_id, timeframe, currency)

            if result:
                # Cachear resultado
                with self._cache_lock:
                    self.cache.set(
                        "kpi:full",
                        result,
                        kpi_id=kpi_id,
                        client_id=client_id,
                        timeframe=timeframe,
                        currency=currency,
                        ttl=1800  # 30 minutos
                    )

                calc_time = round((time.time() - start_time) * 1000, 2)
                logger.info(f"✅ Calculated {kpi_id} in {calc_time}ms")

            return result

        except Exception as e:
            logger.error(f"❌ Error in thread-safe calculation for {kpi_id}: {e}")
            # Fail fast - re-raise to expose the issue
            raise
    
    def _calculate_single_kpi(
        self,
        kpi_def: Dict[str, Any],
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[Dict[str, Any]]:
        """Calcula um único KPI."""
        kpi_id = kpi_def.get('id')
        
        # Calcular valor
        value = self._calculate_kpi_value(
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        if value is None:
            return None
        
        # Gerar dados do gráfico
        chart_data = self._generate_chart_data(
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        # Formatar resposta
        return {
            'id': kpi_id,
            'title': kpi_def.get('name', ''),
            'description': kpi_def.get('description', ''),
            'currentValue': value,
            'format': kpi_def.get('format_type', 'number'),
            'changePercent': self._calculate_change_percent(kpi_id, value, timeframe),
            'trend': self._determine_trend(kpi_id, value),
            'chartType': kpi_def.get('chart_type', 'line'),
            'chartData': chart_data,
            'alert': self._check_alert(kpi_id, value),
            'isPriority': kpi_def.get('is_priority', False),
            'order': kpi_def.get('display_order', 999),
            'category': kpi_def.get('category', 'general'),
            'unit': kpi_def.get('unit', ''),
            'frequency': kpi_def.get('frequency', 'daily')
        }
    
    def _calculate_kpi_value(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[float]:
        """Calcula o valor de um KPI."""
        # Verificar cache
        cached_value = self.cache.get(
            "kpi:value",
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )
        
        if cached_value is not None:
            return cached_value
        
        # Executar query
        value = None
        
        # Executar query dinâmica - fail fast se não existir
        dynamic_query = self.query_manager.get_kpi_query(kpi_id)
        if not dynamic_query:
            raise ValueError(f"No query definition found for KPI: {kpi_id}")

        value = self._execute_query(dynamic_query, timeframe, currency)

        # Fail fast se não conseguir calcular
        if value is None:
            raise ValueError(f"Failed to calculate KPI {kpi_id} with filters: timeframe={timeframe}, currency={currency}")
        
        # Cachear resultado
        if value is not None:
            self.cache.set(
                "kpi:value",
                value,
                timeframe=timeframe,
                kpi_id=kpi_id,
                client_id=client_id,
                currency=currency
            )
        
        return value
    
    def _generate_chart_data(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> List[Dict[str, Any]]:
        """Gera dados para o gráfico do KPI usando dados reais do banco."""
        # Verificar cache
        cached_data = self.cache.get(
            "kpi:chart",
            kpi_id=kpi_id,
            client_id=client_id,
            timeframe=timeframe,
            currency=currency
        )

        if cached_data is not None:
            return cached_data

        # Gerar dados reais do banco de dados
        chart_data = self._get_real_chart_data(kpi_id, client_id, timeframe, currency)

        if not chart_data:
            # Fail fast - não retornar dados mock
            raise ValueError(f"No chart data available for KPI {kpi_id} with filters: timeframe={timeframe}, currency={currency}")

        # Cachear resultado
        self.cache.set(
            "kpi:chart",
            chart_data,
            timeframe=timeframe,
            kpi_id=kpi_id,
            client_id=client_id,
            currency=currency
        )

        return chart_data

    def _get_real_chart_data(
        self,
        kpi_id: str,
        client_id: str,
        timeframe: str,
        currency: str
    ) -> List[Dict[str, Any]]:
        """Obtém dados reais do banco para gráficos."""
        try:
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            from sqlalchemy import text
            from datetime import datetime, timedelta

            # Conectar ao banco
            db_config = load_db_config(setor="cambio", cliente="L2M")
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)

            # Determinar período para dados históricos
            if timeframe == "week":
                days_back = 7
            elif timeframe == "month":
                days_back = 30
            else:
                days_back = 7

            # Obter filtros SQL
            currency_sql = self._get_currency_sql(currency)

            # Query para dados históricos por dia
            query = f"""
            SELECT
                DATE(data_operacao) as date,
                COUNT(*) as transaction_count,
                COALESCE(SUM(valor_me), 0) as daily_volume,
                COALESCE(AVG(valor_me), 0) as daily_avg_ticket
            FROM boleta
            WHERE data_operacao >= CURRENT_DATE - INTERVAL '{days_back} days'
            AND ({currency_sql})
            AND valor_me IS NOT NULL
            AND valor_me > 0
            GROUP BY DATE(data_operacao)
            ORDER BY date
            """

            chart_data = []
            with engine.connect() as conn:
                result = conn.execute(text(query))
                rows = result.fetchall()

                for row in rows:
                    date_obj = row[0]

                    # Escolher valor baseado no KPI
                    if kpi_id == "total_volume":
                        value = float(row[2])  # daily_volume
                    elif kpi_id == "average_ticket":
                        value = float(row[3])  # daily_avg_ticket
                    else:
                        value = float(row[1])  # transaction_count

                    chart_data.append({
                        "name": date_obj.strftime("%d/%m"),
                        "value": round(value, 2),
                        "date": date_obj.strftime("%Y-%m-%d")
                    })

            return chart_data

        except Exception as e:
            logger.error(f"❌ Error getting real chart data for {kpi_id}: {e}")
            # Fail fast - re-raise to expose the issue
            raise

    def _get_timeframe_sql(self, timeframe: str) -> str:
        """Converte timeframe para filtro SQL."""
        mapping = {
            '1d': "data_operacao >= (SELECT MAX(data_operacao) FROM boleta)",
            'week': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '7 days' FROM boleta)",
            'month': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '30 days' FROM boleta)",
            'quarter': "data_operacao >= (SELECT MAX(data_operacao) - INTERVAL '90 days' FROM boleta)"
        }
        return mapping.get(timeframe, mapping['week'])
    
    def _get_currency_sql(self, currency: str) -> str:
        """Converte currency para filtro SQL."""
        if currency == 'all':
            return "1=1"

        # Mapear códigos de moeda para IDs
        currency_mapping = {
            'USD': 4,   # Dólar Americano
            'EUR': 14,  # Euro
            'BRL': 1,   # Real Brasileiro
            'GBP': 3    # Libra Esterlina
        }

        currency_id = currency_mapping.get(currency.upper())
        if currency_id:
            return f"id_moeda = {currency_id}"
        else:
            # Fallback para busca por símbolo na tabela boleta_moeda
            return f"""id_moeda IN (
                SELECT id FROM boleta_moeda
                WHERE UPPER(simbolo) = '{currency.upper()}'
            )"""
    
    def invalidate_kpis(
        self,
        kpi_ids: Optional[List[str]] = None,
        client_id: Optional[str] = None,
        timeframe: Optional[str] = None,
        currency: Optional[str] = None
    ):
        """
        Invalida cache de KPIs específicos ou por padrão.
        
        Args:
            kpi_ids: Lista de IDs para invalidar (None = todos)
            client_id: Cliente específico (None = todos)
            timeframe: Timeframe específico (None = todos)
            currency: Moeda específica (None = todas)
        """
        # Invalidar dashboard cache
        if client_id:
            self.cache.invalidate("kpi:dashboard", client_id=client_id)
        else:
            self.cache.invalidate("kpi:dashboard")
        
        # Invalidar KPI values e charts
        if kpi_ids:
            for kpi_id in kpi_ids:
                if client_id and timeframe and currency:
                    # Invalidação específica
                    self.cache.invalidate(
                        "kpi:value",
                        kpi_id=kpi_id,
                        client_id=client_id,
                        timeframe=timeframe,
                        currency=currency
                    )
                    self.cache.invalidate(
                        "kpi:chart",
                        kpi_id=kpi_id,
                        client_id=client_id,
                        timeframe=timeframe,
                        currency=currency
                    )
                else:
                    # Invalidação por padrão
                    self.cache.invalidate("kpi:value", pattern=kpi_id)
                    self.cache.invalidate("kpi:chart", pattern=kpi_id)
        else:
            # Invalidar todos
            self.cache.invalidate("kpi:value")
            self.cache.invalidate("kpi:chart")
        
        logger.info(f"🗑️ Cache de KPIs invalidado")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do cache."""
        return self.cache.get_detailed_stats()
    
    # Métodos auxiliares (simplificados para exemplo)
    
    def _load_kpi_definitions(
        self,
        sector: str,
        category: Optional[str],
        priority_only: bool
    ) -> List[Dict[str, Any]]:
        """Carrega definições de KPIs do arquivo JSON."""
        try:
            # Usar o query manager para obter as definições
            all_kpis = self.query_manager.get_all_kpi_definitions()

            # Lista de KPIs prioritários (críticos)
            priority_kpis = [
                'total_volume',
                'average_ticket',
                'average_spread',
                'conversion_rate',
                'retention_rate',
                'gross_margin'
            ]

            # Filtrar KPIs
            filtered_kpis = []
            for kpi in all_kpis:
                kpi_id = kpi.get('id')

                # Aplicar filtro de prioridade
                if priority_only and kpi_id not in priority_kpis:
                    continue

                # Aplicar filtro de categoria
                if category and kpi.get('category') != category:
                    continue

                # Marcar como prioritário
                kpi['is_priority'] = kpi_id in priority_kpis

                # Definir ordem de exibição
                if kpi_id in priority_kpis:
                    kpi['display_order'] = priority_kpis.index(kpi_id) + 1
                else:
                    kpi['display_order'] = 999

                filtered_kpis.append(kpi)

            # Ordenar por prioridade
            filtered_kpis.sort(key=lambda x: x.get('display_order', 999))

            return filtered_kpis

        except Exception as e:
            logger.error(f"❌ Erro carregando definições de KPIs: {e}")
            # Fail fast - não retornar fallback
            raise ValueError(f"Failed to load KPI definitions: {e}")
    
    def _execute_query(self, query: str, timeframe: str, currency: str) -> Optional[float]:
        """Executa query SQL com filtros."""
        try:
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            
            # Conectar ao banco
            db_config = load_db_config(setor="cambio", cliente="L2M")
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)
            
            # Substituir placeholders de filtros
            timeframe_sql = self._get_timeframe_sql(timeframe)
            currency_sql = self._get_currency_sql(currency)
            
            # Aplicar filtros na query
            query_with_filters = query
            if ':timeframe_filter' in query:
                query_with_filters = query_with_filters.replace(':timeframe_filter', timeframe_sql)
            if ':currency_filter' in query:
                query_with_filters = query_with_filters.replace(':currency_filter', currency_sql)
            
            # Executar query
            with engine.connect() as conn:
                result = conn.execute(text(query_with_filters))
                row = result.fetchone()
                
                if row and row[0] is not None:
                    return float(row[0])
                    
            return None
            
        except Exception as e:
            logger.error(f"❌ Error executing query: {e}")
            # Fail fast - re-raise to expose the issue
            raise
    

    
    def _calculate_change_percent(self, kpi_id: str, current_value: float, timeframe: str) -> float:
        """Calcula percentual de mudança usando dados reais."""
        try:
            from src.tools.db_utils import load_db_config, build_connection_string, get_engine
            from sqlalchemy import text

            # Conectar ao banco
            db_config = load_db_config(setor="cambio", cliente="L2M")
            connection_string = build_connection_string(db_config)
            engine = get_engine(connection_string)

            # Determinar período anterior
            if timeframe == "week":
                current_period = "CURRENT_DATE - INTERVAL '7 days'"
                previous_period = "CURRENT_DATE - INTERVAL '14 days' AND CURRENT_DATE - INTERVAL '7 days'"
            elif timeframe == "month":
                current_period = "CURRENT_DATE - INTERVAL '30 days'"
                previous_period = "CURRENT_DATE - INTERVAL '60 days' AND CURRENT_DATE - INTERVAL '30 days'"
            else:
                current_period = "CURRENT_DATE - INTERVAL '7 days'"
                previous_period = "CURRENT_DATE - INTERVAL '14 days' AND CURRENT_DATE - INTERVAL '7 days'"

            # Query para valor do período anterior
            if kpi_id == "total_volume":
                query = f"""
                SELECT COALESCE(SUM(valor_me), 0) as previous_value
                FROM boleta
                WHERE data_operacao BETWEEN {previous_period}
                AND valor_me IS NOT NULL
                AND valor_me > 0
                """
            elif kpi_id == "average_ticket":
                query = f"""
                SELECT COALESCE(AVG(valor_me), 0) as previous_value
                FROM boleta
                WHERE data_operacao BETWEEN {previous_period}
                AND valor_me IS NOT NULL
                AND valor_me > 0
                """
            else:
                # Para outros KPIs, retornar 0 se não implementado
                return 0.0

            with engine.connect() as conn:
                result = conn.execute(text(query))
                row = result.fetchone()
                previous_value = float(row[0]) if row and row[0] else 0

            # Calcular percentual de mudança
            if previous_value == 0:
                return 0.0

            change_percent = ((current_value - previous_value) / previous_value) * 100
            return round(change_percent, 2)

        except Exception as e:
            logger.error(f"❌ Error calculating change percent for {kpi_id}: {e}")
            # Fail fast - não retornar valor mock
            raise ValueError(f"Failed to calculate change percent for KPI {kpi_id}")
    
    def _determine_trend(self, kpi_id: str, value: float) -> str:
        """Determina tendência do KPI."""
        # TODO: Implementar análise real
        import random
        return random.choice(['up', 'down', 'stable'])
    
    def _check_alert(self, kpi_id: str, value: float) -> Optional[Dict[str, Any]]:
        """Verifica alertas para o KPI."""
        # TODO: Implementar verificação real
        return None


# Função helper para obter instância do serviço
def get_kpi_service_refactored() -> KpiCalculationServiceRefactored:
    """Retorna instância do serviço de KPIs refatorado."""
    return KpiCalculationServiceRefactored() 
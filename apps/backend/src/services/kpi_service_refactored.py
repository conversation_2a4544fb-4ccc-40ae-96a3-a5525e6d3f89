
import logging
from typing import Dict, List, Any, Optional
from datetime import date, timedelta
from sqlmodel import Session
from src.caching.unified_cache_system import get_unified_cache
from src.repositories.kpi_repository import KpiRepository
from src.utils.learning_db_utils import get_db_manager

logger = logging.getLogger(__name__)

class KpiService:
    def __init__(self):
        self.cache = get_unified_cache()
        self.db_manager = get_db_manager()
        logger.info("✅ KPI Service (Refactored) initialized.")

    def get_session(self) -> Session:
        return Session(self.db_manager.engine)

    def get_dashboard_kpis(
        self,
        client_id: str,
        timeframe: str = "week",
        currency: str = "all"
    ) -> List[Dict[str, Any]]:
        logger.info(f"📊 Calculating dashboard KPIs for client {client_id}...")
        
        cache_key_params = {
            "client_id": client_id,
            "timeframe": timeframe,
            "currency": currency
        }
        
        cached_result = self.cache.get("kpi:dashboard", **cache_key_params)
        if cached_result:
            logger.info(f"✅ Cache hit for dashboard KPIs.")
            return cached_result

        kpi_definitions = self._load_kpi_definitions()
        
        kpis = []
        with self.get_session() as session:
            repo = KpiRepository(session)
            for kpi_def in kpi_definitions:
                kpi = self._calculate_single_kpi(
                    repo,
                    kpi_def,
                    client_id,
                    timeframe,
                    currency
                )
                if kpi:
                    kpis.append(kpi)
        
        self.cache.set("kpi:dashboard", kpis, **cache_key_params)
        logger.info(f"✅ Calculated and cached {len(kpis)} KPIs.")
        return kpis

    def _calculate_single_kpi(
        self,
        repo: KpiRepository,
        kpi_def: Dict[str, Any],
        client_id: str,
        timeframe: str,
        currency: str
    ) -> Optional[Dict[str, Any]]:
        kpi_id = kpi_def['id']
        
        cache_key_params = {
            "kpi_id": kpi_id,
            "client_id": client_id,
            "timeframe": timeframe,
            "currency": currency
        }

        cached_kpi = self.cache.get("kpi:full", **cache_key_params)
        if cached_kpi:
            return cached_kpi

        try:
            start_date, end_date = self._get_dates_from_timeframe(timeframe)
            numeric_client_id = 334 if client_id == 'L2M' else int(client_id)

            kpi_data = None
            if kpi_id == 'total_volume':
                kpi_data = repo.get_total_volume(numeric_client_id, start_date, end_date)
            elif kpi_id == 'average_spread':
                kpi_data = repo.get_average_spread(numeric_client_id, start_date, end_date)
            elif kpi_id == 'average_ticket':
                kpi_data = repo.get_average_ticket(numeric_client_id, start_date, end_date)
            elif kpi_id == 'growth_percentage':
                kpi_data = repo.get_growth_percentage(numeric_client_id, start_date, end_date)
            
            if not kpi_data:
                kpi_data = {}

            formatted_kpi = {
                "id": kpi_id,
                "title": kpi_def['name'],
                "currentValue": kpi_data.get(kpi_id, 0.0),
                **kpi_data
            }
            
            self.cache.set("kpi:full", formatted_kpi, **cache_key_params)
            
            return formatted_kpi
        except Exception as e:
            logger.error(f"❌ Error calculating KPI {kpi_id}: {e}")
            return None

    def _get_dates_from_timeframe(self, timeframe: str) -> (date, date):
        end_date = date.today()
        if timeframe == '1d':
            start_date = end_date - timedelta(days=1)
        elif timeframe == 'week':
            start_date = end_date - timedelta(days=7)
        elif timeframe == 'month':
            start_date = end_date - timedelta(days=30)
        elif timeframe == 'quarter':
            start_date = end_date - timedelta(days=90)
        else:
            start_date = end_date - timedelta(days=7)
        return start_date, end_date

    def _load_kpi_definitions(self) -> List[Dict[str, Any]]:
        return [
            {"id": "total_volume", "name": "Total Volume"},
            {"id": "average_spread", "name": "Average Spread"},
            {"id": "average_ticket", "name": "Average Ticket"},
            {"id": "growth_percentage", "name": "Growth Percentage"},
        ]

def get_kpi_service() -> KpiService:
    return KpiService()

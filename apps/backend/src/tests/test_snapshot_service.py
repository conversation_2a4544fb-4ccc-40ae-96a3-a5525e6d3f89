"""
Unit Tests for Snapshot Service
===============================

Tests for the snapshot generation and management functionality.
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from src.services.snapshot_service import SnapshotService
from src.config.kpi_definitions import CRITICAL_KPIS


class TestSnapshotService:
    """Test cases for SnapshotService."""
    
    @pytest.fixture
    def mock_kpi_service(self):
        """Mock KPI service for testing."""
        mock_service = Mock()
        mock_service.calculate_kpi_value_from_dict.return_value = {
            'currentValue': 1000000.0
        }
        return mock_service
    
    @pytest.fixture
    def mock_db_manager(self):
        """Mock database manager for testing."""
        mock_manager = Mock()
        mock_session = Mock()

        # Create a proper context manager mock
        context_manager = Mock()
        context_manager.__enter__ = Mock(return_value=mock_session)
        context_manager.__exit__ = Mock(return_value=None)
        mock_manager.get_session.return_value = context_manager

        return mock_manager
    
    @pytest.fixture
    def sample_kpi_definitions(self):
        """Sample KPI definitions for testing."""
        return [
            {
                'id': 'total_volume',
                'name': 'Volume Total Negociado',
                'description': 'Mede o tamanho da operação',
                'category': 'operational',
                'format_type': 'currency',
                'unit': 'R$'
            },
            {
                'id': 'average_spread',
                'name': 'Spread Médio',
                'description': 'Principal indicador de rentabilidade',
                'category': 'financial',
                'format_type': 'percentage',
                'unit': '%'
            }
        ]
    
    @pytest.fixture
    def snapshot_service(self, mock_kpi_service, mock_db_manager):
        """Create snapshot service with mocked dependencies."""
        with patch('src.services.snapshot_service.get_kpi_service', return_value=mock_kpi_service), \
             patch('src.services.snapshot_service.get_db_manager', return_value=mock_db_manager):
            
            service = SnapshotService()
            # Use temporary directory for testing
            service.snapshot_dir = Path(tempfile.mkdtemp())
            return service
    
    def test_critical_kpi_ids_configuration(self):
        """Test that critical KPI IDs are properly configured."""
        critical_ids = CRITICAL_KPIS

        assert len(critical_ids) >= 6
        assert 'total_volume' in critical_ids
        assert 'average_spread' in critical_ids
        assert 'conversion_rate' in critical_ids
        assert 'average_ticket' in critical_ids
        assert 'retention_rate' in critical_ids
    
    def test_is_critical_kpi(self):
        """Test critical KPI identification."""
        assert 'total_volume' in CRITICAL_KPIS
        assert 'average_spread' in CRITICAL_KPIS
        assert 'non_critical_kpi' not in CRITICAL_KPIS
    
    def test_format_kpi_value_currency(self, snapshot_service):
        """Test currency formatting."""
        # Test billions
        result = snapshot_service._format_kpi_value(2500000000, 'currency')
        assert result == 'R$ 2.5B'
        
        # Test millions
        result = snapshot_service._format_kpi_value(1500000, 'currency')
        assert result == 'R$ 1.5M'
        
        # Test thousands
        result = snapshot_service._format_kpi_value(50000, 'currency')
        assert result == 'R$ 50,000'
    
    def test_format_kpi_value_percentage(self, snapshot_service):
        """Test percentage formatting."""
        result = snapshot_service._format_kpi_value(25.5, 'percentage')
        assert result == '25.5%'
        
        result = snapshot_service._format_kpi_value(0.25, 'percentage')
        assert result == '0.2%'
    
    def test_format_kpi_value_number(self, snapshot_service):
        """Test number formatting."""
        result = snapshot_service._format_kpi_value(1500, 'number')
        assert result == '1,500'
        
        result = snapshot_service._format_kpi_value(50, 'number')
        assert result == '50'
    
    def test_format_kpi_value_none(self, snapshot_service):
        """Test handling of None values."""
        result = snapshot_service._format_kpi_value(None, 'currency')
        assert result == 'N/A'
    
    def test_get_kpi_icon(self, snapshot_service):
        """Test KPI icon mapping."""
        assert snapshot_service._get_kpi_icon('total_volume') == 'TrendingUp'
        assert snapshot_service._get_kpi_icon('average_spread') == 'DollarSign'
        assert snapshot_service._get_kpi_icon('unknown_kpi') == 'BarChart'
    
    def test_generate_error_snapshot(self, snapshot_service):
        """Test error snapshot generation."""
        error_snapshot = snapshot_service._generate_error_snapshot("Test error")
        
        assert error_snapshot['metadata']['error'] is True
        assert error_snapshot['metadata']['error_message'] == "Test error"
        assert error_snapshot['kpis'] == {}
        assert error_snapshot['summary']['success_rate'] == 0
    
    @patch('src.services.snapshot_service.datetime')
    def test_save_and_get_latest_snapshot(self, mock_datetime, snapshot_service):
        """Test snapshot saving and retrieval."""
        # Mock datetime
        mock_datetime.now.return_value.strftime.side_effect = lambda fmt: {
            '%Y%m%d': '20250706',
            '%Y-%m-%d': '2025-07-06',
            '%H:%M:%S': '10:30:00'
        }[fmt]
        
        test_snapshot = {
            'metadata': {'date': '2025-07-06', 'time': '10:30:00'},
            'kpis': {'test_kpi': {'value': 100}},
            'summary': {'total_calculated': 1}
        }
        
        # Save snapshot
        snapshot_service._save_snapshot(test_snapshot)
        
        # Check files were created
        assert (snapshot_service.snapshot_dir / 'latest.json').exists()
        assert (snapshot_service.snapshot_dir / 'snapshot_20250706.json').exists()
        
        # Test retrieval
        retrieved = snapshot_service.get_latest_snapshot()
        assert retrieved == test_snapshot
    
    def test_get_latest_snapshot_not_exists(self, snapshot_service):
        """Test getting latest snapshot when none exists."""
        result = snapshot_service.get_latest_snapshot()
        assert result is None


import pytest
from unittest.mock import Mock, patch
from src.services.snapshot_service import SnapshotService

class TestSnapshotService:
    @pytest.fixture
    def mock_kpi_service(self):
        mock_service = Mock()
        mock_service.get_dashboard_kpis.return_value = []
        return mock_service
    
    @pytest.fixture
    def mock_db_manager(self):
        mock_manager = Mock()
        return mock_manager
    
    @pytest.fixture
    def snapshot_service(self, mock_kpi_service, mock_db_manager):
        with patch('src.services.snapshot_service.get_kpi_service', return_value=mock_kpi_service):
            with patch('src.services.snapshot_service.get_db_manager', return_value=mock_db_manager):
                return SnapshotService()

    def test_initialization(self, snapshot_service):
        assert snapshot_service is not None

import pytest
from fastapi.testclient import TestClient
from src.interfaces.api import app

client = TestClient(app)

def test_pipeline_e2e_ask():
    response = client.post("/ask", json={"question": "Qual o volume total?"})
    assert response.status_code == 200
    result = response.json()
    assert "query_id" in result
    assert "question" in result
    assert "sql_query" in result
    assert "results" in result
    assert "direct_answer" in result

def test_pipeline_e2e_kpis():
    response = client.get("/api/dashboard/kpis")
    assert response.status_code == 200
    result = response.json()
    assert isinstance(result, list)

import pytest
from fastapi.testclient import TestClient
from src.interfaces.api import app

client = TestClient(app)

def test_currency_filters():
    """Testa se os filtros de moeda produzem valores diferentes."""
    currencies = ["all", "USD", "EUR"]
    
    for currency in currencies:
        params = {
            "timeframe": "week",
            "currency": currency,
        }
        response = client.get("/api/dashboard/kpis", params=params)
        assert response.status_code == 200

def test_timeframe_filters():
    """Testa se os filtros de timeframe produzem valores diferentes."""
    timeframes = ["week", "month"]
    
    for timeframe in timeframes:
        params = {
            "timeframe": timeframe,
            "currency": "all",
        }
        response = client.get("/api/dashboard/kpis", params=params)
        assert response.status_code == 200

def test_cache_invalidation():
    """Testa se o cache está sendo invalidado corretamente."""
    params = {
        "timeframe": "week",
        "currency": "USD",
    }
    
    response1 = client.get("/api/dashboard/kpis", params=params)
    assert response1.status_code == 200
    
    response2 = client.get("/api/dashboard/kpis", params=params)
    assert response2.status_code == 200
    
    params["currency"] = "EUR"
    response3 = client.get("/api/dashboard/kpis", params=params)
    assert response3.status_code == 200

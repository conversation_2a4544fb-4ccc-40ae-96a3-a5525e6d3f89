import time
import sys
sys.path.insert(0, '.')

from src.caching.unified_cache_system import get_unified_cache
from src.services.kpi_service_refactored import get_kpi_service


def test_cache_system():
    """Testa o sistema de cache unificado."""
    print("🧪 Testando Sistema de Cache Unificado\n")
    
    cache = get_unified_cache()
    cache.clear()
    
    print("1️⃣ Teste de Cache Básico")
    cache.set("kpi:value", 12345.67, kpi_id="total_volume", timeframe="week")
    value = cache.get("kpi:value", kpi_id="total_volume", timeframe="week")
    print(f"✅ Valor cacheado: {value}")
    
    miss_value = cache.get("kpi:value", kpi_id="total_volume", timeframe="month")
    print(f"❌ Cache miss (diferente timeframe): {miss_value}")
    
    stats = cache.get_stats()
    print(f"\n📊 Estatísticas: Hits={stats['hits']}, Misses={stats['misses']}")
    
    print("\n" + "=" * 50 + "\n")
    
    print("5️⃣ Teste do KPI Service Refatorado")
    try:
        kpi_service = get_kpi_service()
        
        start = time.time()
        kpis1 = kpi_service.get_dashboard_kpis(client_id="L2M", timeframe="week")
        time1 = (time.time() - start) * 1000
        print(f"❌ Primeira chamada (cache miss): {len(kpis1)} KPIs em {time1:.2f}ms")
        
        start = time.time()
        kpis2 = kpi_service.get_dashboard_kpis(client_id="L2M", timeframe="week")
        time2 = (time.time() - start) * 1000
        print(f"✅ Segunda chamada (cache hit): {len(kpis2)} KPIs em {time2:.2f}ms")
        
        if time2 > 0:
            speedup = time1 / time2
            print(f"🚀 Speedup: {speedup:.1f}x mais rápido com cache!")
        
    except Exception as e:
        print(f"⚠️  Erro ao testar KPI Service: {e}")
    
    print("\n✅ Testes concluídos!")


if __name__ == "__main__":
    test_cache_system()
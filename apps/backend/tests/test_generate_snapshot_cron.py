import pytest
from unittest.mock import Mock, patch
from generate_snapshot_cron import main

class TestGenerateSnapshotCron:
    @patch('generate_snapshot_cron.check_database_connectivity')
    @patch('generate_snapshot_cron.generate_snapshot_task')
    def test_main_success(self, mock_generate_snapshot, mock_check_db):
        mock_check_db.return_value = True
        mock_generate_snapshot.return_value = True
        with pytest.raises(SystemExit) as e:
            main()
        assert e.value.code == 0

    @patch('generate_snapshot_cron.check_database_connectivity')
    def test_main_db_error(self, mock_check_db):
        mock_check_db.return_value = False
        with pytest.raises(SystemExit) as e:
            main()
        assert e.value.code == 1
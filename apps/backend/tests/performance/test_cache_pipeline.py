import os
import sys
import time
import pytest
from fastapi.testclient import TestClient

# Adiciona o diretório raiz do projeto ao sys.path
# para garantir que os módulos sejam encontrados
backend_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, backend_dir)

from src.interfaces.api import app

client = TestClient(app)

def run_test():
    # Primeira chamada (deve ser mais lenta)
    start_time_first = time.time()
    response_first = client.get("/api/dashboard/kpis?priority_only=false")
    end_time_first = time.time()
    duration_first = end_time_first - start_time_first
    assert response_first.status_code == 200
    
    # Segunda chamada (deve ser mais rápida, com cache)
    start_time_second = time.time()
    response_second = client.get("/api/dashboard/kpis?priority_only=false")
    end_time_second = time.time()
    duration_second = end_time_second - start_time_second
    assert response_second.status_code == 200
    
    return duration_first, duration_second

def test_cache_performance():
    # Roda o teste 5 vezes para aquecer e obter uma média
    durations = [run_test() for _ in range(5)]
    
    avg_first = sum(d[0] for d in durations) / len(durations)
    avg_second = sum(d[1] for d in durations) / len(durations)
    
    print(f"\n--- Resultados do Teste de Performance do Cache ---")
    print(f"Tempo médio da 1ª chamada (sem cache): {avg_first:.4f} segundos")
    print(f"Tempo médio da 2ª chamada (com cache): {avg_second:.4f} segundos")
    
    # Verifica se a segunda chamada foi pelo menos 1.5x mais rápida
    assert avg_second < avg_first / 1.5, "O cache não resultou em uma melhoria de performance significativa (1.5x)."
    print(f"✅ O cache melhorou a performance em {avg_first / avg_second:.2f}x")

if __name__ == "__main__":
    pytest.main([__file__])
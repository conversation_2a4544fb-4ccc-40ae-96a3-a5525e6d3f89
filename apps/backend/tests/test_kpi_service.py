import pytest
from unittest.mock import Mock, patch
from decimal import Decimal
from datetime import datetime

from src.services.kpi_service_refactored import KpiService

class TestKpiService:
    @pytest.fixture
    def mock_db_manager(self):
        mock_db = Mock()
        mock_db.get_connection.return_value.__enter__ = Mock()
        mock_db.get_connection.return_value.__exit__ = Mock()
        return mock_db
    
    @pytest.fixture
    def kpi_service(self, mock_db_manager):
        with patch('src.services.kpi_service_refactored.get_db_manager', return_value=mock_db_manager):
            return KpiService()
    
    def test_get_dashboard_kpis_success(self, kpi_service):
        with patch.object(kpi_service, 'get_session') as mock_get_session:
            mock_session = Mock()
            mock_get_session.return_value.__enter__.return_value = mock_session
            
            mock_repo = Mock()
            mock_repo.get_total_volume.return_value = {'total_volume': 1000.0}
            mock_repo.get_average_spread.return_value = {'average_spread': 0.5}
            mock_repo.get_average_ticket.return_value = {'average_ticket': 250.0}
            mock_repo.get_growth_percentage.return_value = {'growth_percentage': 10.0}
            
            with patch('src.services.kpi_service_refactored.KpiRepository', return_value=mock_repo):
                result = kpi_service.get_dashboard_kpis(client_id='L2M')
        
        assert len(result) > 0
        assert result[0]['id'] == 'total_volume'
        assert result[0]['currentValue'] == 1000.0

import pytest
from fastapi.testclient import TestClient
from src.interfaces.api import app

client = TestClient(app)

def test_get_dashboard_kpis_e2e_success():
    """
    Teste E2E para o endpoint /api/dashboard/kpis.
    Verifica uma resposta bem-sucedida com os parâmetros padrão.
    """
    response = client.get("/api/dashboard/kpis")
    
    assert response.status_code == 200
    data = response.json()
    
    assert isinstance(data, list)
    if len(data) > 0:
        kpi = data[0]
        assert "id" in kpi
        assert "title" in kpi
        assert "currentValue" in kpi
        assert isinstance(kpi["currentValue"], float)

def test_get_dashboard_kpis_e2e_with_filters():
    """
    Testa o endpoint com filtros.
    """
    response = client.get("/api/dashboard/kpis?timeframe=month&currency=USD")
    
    assert response.status_code == 200
    data = response.json()
    
    assert isinstance(data, list)

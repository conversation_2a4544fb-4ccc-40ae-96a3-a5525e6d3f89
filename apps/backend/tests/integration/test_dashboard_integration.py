import pytest
import json
from pathlib import Path

class TestDashboardIntegration:
    def test_kpi_configuration_loading(self):
        config_path = Path("src/config/setores/cambio/kpis-exchange-json.json")
        assert config_path.exists()
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        assert "metadata" in config
        assert "categories" in config
        assert config["metadata"]["version"] == "1.0"

    def test_integration_completeness(self):
        required_files = [
            "src/interfaces/dashboard_api.py",
            "../../apps/frontend/src/lib/api.ts",
            "../../apps/frontend/src/hooks/useKpiData.ts",
            "src/config/setores/cambio/kpis-exchange-json.json"
        ]
        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        assert len(missing_files) == 0, f"Arquivos obrigatórios não encontrados: {missing_files}"
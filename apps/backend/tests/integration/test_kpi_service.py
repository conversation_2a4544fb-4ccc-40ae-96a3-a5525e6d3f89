import pytest
from sqlmodel import Session
from src.services.kpi_service_refactored import KpiService
from src.models.orm_models import <PERSON>let<PERSON>
from src.utils.learning_db_utils import get_db_manager
from datetime import date, timedelta

@pytest.fixture(name="session")
def session_fixture():
    """Cria uma sessão com o banco de dados de aprendizado (Railway)."""
    db_manager = get_db_manager()
    with db_manager.get_session() as session:
        yield session

def test_get_dashboard_kpis_with_real_db(session: Session):
    # Arrange: Inserir um registro de teste no banco de aprendizado
    # Usamos um ID alto para evitar conflitos
    test_boleta = Boleta(id_boleta=9999999, id_cliente=1, data_operacao=date.today(), valor_me=5000, tipo_operacao='COMPRA', taxa_cambio=5.0, taxa_base=5.1, id_moeda=1, id_boleta_status=1, id_funcionario_criador=1)
    session.add(test_boleta)
    session.commit()
    session.refresh(test_boleta)

    kpi_service = KpiService()
    
    # Injetar a sessão de teste no serviço
    kpi_service.get_session = lambda: session

    try:
        # Act: Calcular KPIs
        # Usamos um timeframe que certamente incluirá nosso registro de teste
        end_date = date.today()
        start_date = end_date - timedelta(days=1)
        
        kpis = kpi_service.get_dashboard_kpis(client_id="1", timeframe="1d")

        # Assert: Verificar se o KPI foi calculado corretamente
        assert len(kpis) > 0
        total_volume_kpi = next((kpi for kpi in kpis if kpi['id'] == 'total_volume'), None)
        assert total_volume_kpi is not None
        # O valor deve ser pelo menos o que inserimos
        assert total_volume_kpi['currentValue'] >= 5000.0

    finally:
        # Cleanup: Remover o registro de teste
        session.delete(test_boleta)
        session.commit()

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from starlette.testclient import TestClient
from fastapi import FastAP<PERSON>

from src.api.health_routes import router as health_router


@pytest.fixture
def app():
    app = FastAPI()
    app.include_router(health_router)
    return app


@pytest.fixture
def client(app):
    return TestClient(app)


class TestHealthRoutes:
    def test_health_basic(self, client):
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data["details"]

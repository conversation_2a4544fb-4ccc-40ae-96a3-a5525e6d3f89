import pytest
from unittest.mock import Mock, patch
from src.services.scheduler_service import SchedulerService

class TestSchedulerService:
    @pytest.fixture
    def mock_snapshot_service(self):
        return Mock()

    @pytest.fixture
    def scheduler_service(self, mock_snapshot_service):
        with patch('src.services.scheduler_service.SnapshotService', return_value=mock_snapshot_service):
            return SchedulerService()

    def test_initialization(self, scheduler_service):
        assert scheduler_service is not None